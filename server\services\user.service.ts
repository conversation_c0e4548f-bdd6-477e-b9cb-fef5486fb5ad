import apiClient from '@/server/apiClient';
import authService from './auth.service';
import { IAPIResponse, ISessionUser } from '@/types/api';
import { AddressFormValues, GeneralInfoFormValues, UpdateUserPasswordInput } from '@/validation/schemas/user.schema';
import { IUserTypeMap } from '@/validation/schemas/maps';

export interface ISessionUserResponse {
  user: ISessionUser;
}

export interface IGetGeneralInfo extends GeneralInfoFormValues {
  email: string;
  phone: string;
  userType: IUserTypeMap;
  profilePicture: string;
}

export interface IAddressGet extends AddressFormValues {
  _id: string;
  user: string;
}

interface ISingleAddressResponse {
  address: IAddressGet;
}

const userService = {
  showCurrentUser: async () => await apiClient.get<ISessionUserResponse>('/users/showMe'),
  updateUserPassword: async (data: UpdateUserPasswordInput) => await apiClient.patch<IAPIResponse>('/users/updateUserPassword', data),
  updateProfilePicture: async (data: FormData) => await apiClient.patch<IAPIResponse>('/users/profile-picture', data),
  getGeneralInfo: async () => await apiClient.get<{ generalInfo: IGetGeneralInfo }>('/users/general-info'),
  updateGeneralInfo: async (data: GeneralInfoFormValues) => await apiClient.patch<ISessionUserResponse>('/users/general-info', data),
  logout: () => authService.logout(),
};

const addressService = {
  createAddress: async (data: AddressFormValues) => await apiClient.post<ISingleAddressResponse>('/addresses', data),
  getUserAddresses: async () => await apiClient.get<{ addresses: IAddressGet[] }>('/addresses'),
  getAddressById: async (addressId: string) => await apiClient.get<ISingleAddressResponse>(`/addresses/${addressId}`),
  updateAddress: async (addressId: string, data: AddressFormValues) => await apiClient.patch<ISingleAddressResponse>(`/addresses/${addressId}`, data),
  deleteAddress: async (addressId: string) => await apiClient.delete<IAPIResponse>(`/addresses/${addressId}`),
};

export interface IBusinessLocationDocument {
  _id: string;
  name: string;
  location: string;
  createdAt: Date;
  updatedAt: Date;
}

interface IBusinessLocationSearchResponse {
  businessLocations: IBusinessLocationDocument[];
}

const userBusinessLocationService = {
  searchLocation: async (query: string) => {
    const searchParams = new URLSearchParams({ query });
    return await apiClient.get<IBusinessLocationSearchResponse>(`/business-locations/search?${searchParams.toString()}`);
  },
};

export { userService, addressService, userBusinessLocationService };
