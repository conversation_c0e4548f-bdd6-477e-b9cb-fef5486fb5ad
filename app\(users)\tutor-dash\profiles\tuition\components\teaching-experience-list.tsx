'use client';

import { useState } from 'react';
import { Clock, Home, MapPin, School, Edit, Trash2 } from 'lucide-react';
import { AlertDialog, AlertDialogCancel, AlertDialogContent, AlertDialogTrigger } from '@/components/ui/alert-dialog';
import { SimpleTable } from '@/components/dashboard/tutor-dash/misc';
import TutorDashActionLinks from '@/components/dashboard/tutor-dash/misc/TutorDashActionLinks';
import { ITeachingExperienceDocument } from '@/server/services/tutor/tuition-profile.service';
import { tuitionTypeMap } from '@/validation/schemas/tutor/tuition.maps';
import { useDeleteTeachingExperience } from '@/hooks/tutor/tuition-profile.hooks';
import { toast } from 'react-toastify';
import { Skeleton } from '@/components/ui/skeleton';
import TeachingExperienceForm from './teaching-experience-form';
import { PrimaryModalWithHeader } from '@/components/dashboard/shared/misc';

interface TeachingExperienceListProps {
  experiences: ITeachingExperienceDocument[] | undefined;
  isLoading: boolean;
}

const TeachingExperienceList = ({ experiences, isLoading }: TeachingExperienceListProps) => {
  const [editingExperience, setEditingExperience] = useState<ITeachingExperienceDocument | null>(null);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const deleteExperience = useDeleteTeachingExperience();

  const handleEdit = (experience: ITeachingExperienceDocument) => {
    setEditingExperience(experience);
    setIsEditModalOpen(true);
  };

  const handleDelete = async (id: string) => {
    try {
      await deleteExperience.mutateAsync(id);
      toast.success('Teaching experience deleted successfully!');
    } catch (error: any) {
      toast.error(error?.response?.data?.message || 'Failed to delete experience');
    }
  };

  const closeEditModal = () => {
    setIsEditModalOpen(false);
    setEditingExperience(null);
  };

  if (isLoading) {
    return (
      <div className='space-y-4'>
        {Array.from({ length: 3 }).map((_, index) => (
          <div key={index} className='border rounded-lg p-4'>
            <div className='grid grid-cols-1 md:grid-cols-4 gap-4'>
              <Skeleton className='h-6 w-full' />
              <Skeleton className='h-6 w-full' />
              <Skeleton className='h-6 w-full' />
              <Skeleton className='h-6 w-full' />
            </div>
          </div>
        ))}
      </div>
    );
  }

  if (!experiences || experiences.length === 0) {
    return (
      <div className='text-center py-12 bg-gray-50 rounded-lg border-2 border-dashed border-gray-200'>
        <School className='mx-auto h-12 w-12 text-gray-400 mb-4' />
        <h3 className='text-lg font-medium text-gray-900 mb-2'>No Teaching Experience</h3>
        <p className='text-gray-600 mb-4'>
          You haven't added any teaching experience yet. Click "Add Experience" to get started.
        </p>
      </div>
    );
  }

  // Prepare data for table
  const tableData = experiences.map((exp) => [
    tuitionTypeMap[exp.tuitionType]?.label || exp.tuitionType,
    exp.experienceMonths,
    exp.placeName,
    exp.location,
    <TutorDashActionLinks 
      key={exp._id}
      basePath=''
      id={exp._id}
      edit
      delete
      onEdit={() => handleEdit(exp)}
      onDelete={() => handleDelete(exp._id)}
    />
  ]);

  return (
    <>
      {/* Desktop Table View */}
      <div className='max-md:hidden'>
        <SimpleTable
          headers={['Tuition Type', 'Exp. (M)', 'Place Name', 'Location', 'Actions']}
          rows={tableData}
        />
      </div>

      {/* Mobile Card View */}
      <div className='md:hidden space-y-4'>
        {experiences.map((exp) => (
          <div key={exp._id} className='border rounded-lg p-4 bg-white shadow-sm'>
            <div className='space-y-3'>
              <div className='flex items-center gap-2'>
                <Home strokeWidth={1.5} className='text-primaryColor w-4 h-4' />
                <span className='font-medium text-sm'>Tuition Type:</span>
                <span className='text-gray-600 text-sm'>
                  {tuitionTypeMap[exp.tuitionType]?.label || exp.tuitionType}
                </span>
              </div>
              
              <div className='flex items-center gap-2'>
                <Clock strokeWidth={1.5} className='text-primaryColor w-4 h-4' />
                <span className='font-medium text-sm'>Experience:</span>
                <span className='text-gray-600 text-sm'>{exp.experienceMonths} months</span>
              </div>
              
              <div className='flex items-center gap-2'>
                <School strokeWidth={1.5} className='text-primaryColor w-4 h-4' />
                <span className='font-medium text-sm'>Place:</span>
                <span className='text-gray-600 text-sm'>{exp.placeName}</span>
              </div>
              
              <div className='flex items-center gap-2'>
                <MapPin strokeWidth={1.5} className='text-primaryColor w-4 h-4' />
                <span className='font-medium text-sm'>Location:</span>
                <span className='text-gray-600 text-sm'>{exp.location}</span>
              </div>
              
              <div className='flex justify-end pt-2 border-t'>
                <div className='flex gap-2'>
                  <button
                    onClick={() => handleEdit(exp)}
                    className='p-2 text-blue-600 hover:bg-blue-50 rounded-lg transition-colors'
                    title='Edit'
                  >
                    <Edit className='w-4 h-4' />
                  </button>
                  <button
                    onClick={() => handleDelete(exp._id)}
                    className='p-2 text-red-600 hover:bg-red-50 rounded-lg transition-colors'
                    title='Delete'
                    disabled={deleteExperience.isPending}
                  >
                    <Trash2 className='w-4 h-4' />
                  </button>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Edit Modal */}
      <PrimaryModalWithHeader
        isOpen={isEditModalOpen}
        onClose={closeEditModal}
        title='Edit Teaching Experience'
        maxWidth='max-w-2xl'
      >
        {editingExperience && (
          <TeachingExperienceForm
            experience={editingExperience}
            onCancel={closeEditModal}
          />
        )}
      </PrimaryModalWithHeader>
    </>
  );
};

export default TeachingExperienceList;
