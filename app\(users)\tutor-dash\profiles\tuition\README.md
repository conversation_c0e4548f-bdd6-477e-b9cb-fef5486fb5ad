# Tuition Profile Implementation Plan

## Overview

This document outlines the comprehensive plan for implementing the tuition profile system for tutors, following the same architectural patterns as the education profile system.

## Current State Analysis

### ✅ **Existing Infrastructure**

- **Education Profile System**: Complete implementation with forms, schemas, services, and hooks
- **Service Categories**: `serviceCategoryMap` with schools, colleges, languages, hobbies, it_courses, exams
- **Maps & Constants**: Delivery modes, tutor preferences, languages, experience types
- **Form Components**: Reusable PrimaryInput, PrimarySelect, PrimaryMultiSelect, etc.
- **Business Location Search**: Autocomplete functionality for institutions

### 📋 **Required Implementation**

## 1. Schema & Validation Layer

### A. Tuition Information Schema

```typescript
// validation/schemas/tutor/profiles/tuition-info.schema.ts
- totalTeachingExperience: number (months)
- isFullTimeTeacher: boolean
- teachesSpecialStudents: boolean
- maxTravelDistance: number (km)
- spokenLanguages: string[] (multi-select)
- deliveryModes: IDeliveryModeMap[] (student_house, tutor_house, institute, online)
- location: string
- description: string
```

### B. Teaching Experience Schema

```typescript
// validation/schemas/tutor/profiles/teaching-experience.schema.ts
- tuitionType: ITuitionTypeMap (private, school, college, institute, other)
- experienceMonths: number
- placeName: string (conditional: "Self" for private)
- location: string (conditional: "Self" for private)
- businessLocationId?: string (for autocomplete)
```

### C. What You Teach Schema

```typescript
// validation/schemas/tutor/profiles/teaching-subjects.schema.ts
- serviceCategory: IServiceCategoryMap
- amount: number
- budget: number
- currency: string (default: "INR") - currencyMap

// Conditional fields based on serviceCategory:
// School: boardId, classId, subjectIds[]
// College: streamId, degreeLevelId, degreeId, branchId, subjectIds[]
// Language: languageTypeId, languageId
// Hobby: hobbyTypeId, hobbyId
// Exam: examCategoryId, examId, subjectIds[]
// IT Course: courseTypeId, courseId
```

## 2. Service Layer

### A. Tuition Profile Service

```typescript
// server/services/tutor/tuition-profile.service.ts
interface ITuitionInfoDocument {
  _id: string;
  userId: string;
  totalTeachingExperience: number;
  isFullTimeTeacher: boolean;
  teachesSpecialStudents: boolean;
  maxTravelDistance: number;
  spokenLanguages: string[];
  deliveryModes: IDeliveryModeMap[];
  location: string;
  description: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

interface ITeachingExperienceDocument {
  _id: string;
  userId: string;
  tuitionType: ITuitionTypeMap;
  experienceMonths: number;
  placeName: string;
  location: string;
  businessLocationId?: string;
  businessLocationDetails?: IBusinessLocationDocument;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

interface ITeachingSubjectDocument {
  _id: string;
  userId: string;
  serviceCategory: IServiceCategoryMap;
  amount: number;
  budget: number;
  currency: string;

  // School fields
  boardId?: string;
  classId?: string;
  subjectIds?: string[];

  // College fields
  streamId?: string;
  degreeLevelId?: string;
  degreeId?: string;
  branchId?: string;

  // Language fields
  languageTypeId?: string;
  languageId?: string;

  // Hobby fields
  hobbyTypeId?: string;
  hobbyId?: string;

  // Exam fields
  examCategoryId?: string;
  examId?: string;

  // IT Course fields
  courseTypeId?: string;
  courseId?: string;

  // Populated fields

  // School fields
  boardDetails?: IBoardDocument;
  classDetails?: IClassDocument;
  subjectDetails?: ISubjectDocument[];

  // College fields
  streamDetails?: IStreamDocument;
  degreeLevelDetails?: IDegreeLevelDocument;
  degreeDetails?: IDegreeDocument;
  branchDetails?: IBranchDocument;
  collegeSubjectDetails?: ICollegeSubjectDocument[];

  // Language fields
  languageTypeDetails?: ILanguageTypeDocument;
  languageDetails?: ILanguageDocument;
  // Hobby fields
  hobbyTypeDetails?: IHobbyTypeDocument;
  hobbyDetails?: IHobbyDocument;
  // Exam fields
  examCategoryDetails?: IExamCategoryDocument;
  examDetails?: IExamDocument;
  examSubjectDetails?: IExamSubjectDocument[];
  // IT Course fields
  courseTypeDetails?: ICourseTypeDocument;
  courseDetails?: ICourseDocument;
  // Common fields
  businessLocationDetails?: IBusinessLocationDocument;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}
```

### B. API Endpoints

```typescript
// Tuition Info
GET    /tutor/tuition-info
POST   /tutor/tuition-info
PATCH  /tutor/tuition-info/:id
DELETE /tutor/tuition-info/:id

// Teaching Experience
GET    /tutor/teaching-experience
POST   /tutor/teaching-experience
PATCH  /tutor/teaching-experience/:id
DELETE /tutor/teaching-experience/:id

// Teaching Subjects
GET    /tutor/teaching-subjects
POST   /tutor/teaching-subjects
PATCH  /tutor/teaching-subjects/:id
DELETE /tutor/teaching-subjects/:id
```

## 3. React Query Hooks

### A. Tuition Info Hooks

```typescript
// hooks/tutor/tuition-profile.hooks.ts
export function useGetTuitionInfo();
export function useCreateTuitionInfo();
export function useUpdateTuitionInfo();
export function useDeleteTuitionInfo();

export function useGetAllTeachingExperience();
export function useCreateTeachingExperience();
export function useUpdateTeachingExperience();
export function useDeleteTeachingExperience();

export function useGetAllTeachingSubjects();
export function useCreateTeachingSubject();
export function useUpdateTeachingSubject();
export function useDeleteTeachingSubject();
```

## 4. Maps & Constants

### A. Tuition Type Map

```typescript
// validation/schemas/tutor/tuition.maps.ts
export const tuitionTypeMap = {
  private: { key: 'private', label: 'Private Tuition' },
  school: { key: 'school', label: 'School' },
  college: { key: 'college', label: 'College' },
  institute: { key: 'institute', label: 'Institute' },
  other: { key: 'other', label: 'Other' },
} as const;
```

### B. Currency Map

```typescript
export const currencyMap = {
  inr: { key: 'inr', label: 'INR', symbol: '₹' },
  usd: { key: 'usd', label: 'USD', symbol: '$' },
} as const;
```

## 5. Component Structure

### A. File Organization

```
app/(users)/tutor-dash/profiles/tuition/
├── _client.tsx                    // Main client component
├── page.tsx                       // Page wrapper
├── tuition-info-form.tsx         // Tuition information form
├── tuition-info-card.tsx         // Tuition information display
├── teaching-experience-form.tsx   // Teaching experience form
├── teaching-experience-list.tsx   // Teaching experience list
├── teaching-experience-card.tsx   // Teaching experience card
├── teaching-subjects-form.tsx     // Teaching subjects form
├── teaching-subjects-list.tsx     // Teaching subjects list
├── teaching-subjects-card.tsx     // Teaching subjects card
└── README.md                      // This file
```

### B. Component Features

- **Tabbed Interface**: Three main tabs (Info, Experience, Subjects)
- **Modal Forms**: Add/edit functionality in modal popups
- **Responsive Design**: Mobile-friendly with proper breakpoints
- **Business Autocomplete**: Institution search for teaching experience
- **Conditional Logic**: Dynamic fields based on selections
- **Validation**: Real-time form validation with error handling
- **Loading States**: Proper loading and error states

## 6. Design Patterns

### A. Follow Education Profile Patterns

- Use same modal structure as `EducationForm`
- Implement similar card designs as `EducationCard`
- Follow same service/hook patterns as `profile.service.ts`
- Use consistent validation patterns as `education-detail.schema.ts`

### B. Business Logic

- **Private Tuition**: Auto-set placeName and location to "Self"
- **Institution Types**: Show business autocomplete for place selection
- **Service Categories**: Dynamic form fields based on selected category
- **Amount/Budget**: Support multiple currencies with proper validation

## 7. Implementation Priority

### Phase 1: Core Infrastructure

1. Create schemas and validation
2. Implement service layer
3. Create React Query hooks
4. Add maps and constants

### Phase 2: UI Components

1. Tuition info form and display
2. Teaching experience CRUD
3. Teaching subjects CRUD
4. Responsive design implementation

### Phase 3: Advanced Features

1. Business location autocomplete
2. Advanced filtering and search
3. Data export functionality
4. Analytics and reporting