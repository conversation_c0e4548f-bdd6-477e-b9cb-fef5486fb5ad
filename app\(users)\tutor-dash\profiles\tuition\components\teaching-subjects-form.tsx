'use client';

import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { toast } from 'react-toastify';
import { Form } from '@/components/ui/form';
import { useState, useEffect } from 'react';
import { PrimaryInput, PrimarySelect, PrimaryMultiSelectForm, SubmitButton, CancelButton } from '@/components/forms';
import { createTeachingSubjectSchema, CreateTeachingSubjectInput } from '@/validation/schemas/tutor/profiles/teaching-subjects.schema';
import { useCreateTeachingSubject, useUpdateTeachingSubject } from '@/hooks/tutor/tuition-profile.hooks';
import { currencyMap } from '@/validation/schemas/tutor/tuition.maps';
import { serviceCategoryMap } from '@/validation/schemas/education/index.maps';
import { ITeachingSubjectDocument } from '@/server/services/tutor/tuition-profile.service';

// Import hooks for dependent dropdowns
import { useGetAllBoards, useGetAllClasses, useGetAllSubjects } from '@/hooks/education/school.hooks';
import {
  useGetAllStreams,
  useGetAllDegreeLevels,
  useGetAllDegrees,
  useGetAllBranches,
  useGetAllCollegeSubjects,
} from '@/hooks/education/college.hooks';
import { useGetAllLanguageTypes, useGetAllLanguages } from '@/hooks/education/language.hooks';
import { useGetAllHobbyTypes, useGetAllHobbies } from '@/hooks/education/hobby.hooks';
import { useGetAllExamCategories, useGetAllExams, useGetAllExamSubjects } from '@/hooks/education/exam.hooks';
import { useGetAllCourseTypes, useGetAllCourses } from '@/hooks/education/course.hooks';

interface TeachingSubjectsFormProps {
  subject?: ITeachingSubjectDocument;
  selectedCategory?: string;
  onCancel?: () => void;
}

const TeachingSubjectsForm = ({ subject, selectedCategory, onCancel }: TeachingSubjectsFormProps) => {
  const createSubject = useCreateTeachingSubject();
  const updateSubject = useUpdateTeachingSubject();

  const isEditing = !!subject;

  const form = useForm<CreateTeachingSubjectInput>({
    resolver: zodResolver(createTeachingSubjectSchema),
    defaultValues: {
      serviceCategory: (subject?.serviceCategory || selectedCategory || 'school') as any,
      amount: subject?.amount || 0,
      budget: subject?.budget || 0,
      currency: subject?.currency || 'inr',
      // School fields
      boardId: subject?.boardId || '',
      classId: subject?.classId || '',
      subjectIds: subject?.subjectIds || [],
      // College fields
      streamId: subject?.streamId || '',
      degreeLevelId: subject?.degreeLevelId || '',
      degreeId: subject?.degreeId || '',
      branchId: subject?.branchId || '',
      collegeSubjectIds: subject?.collegeSubjectIds || [],
      // Language fields
      languageTypeId: subject?.languageTypeId || '',
      languageId: subject?.languageId || '',
      // Hobby fields
      hobbyTypeId: subject?.hobbyTypeId || '',
      hobbyId: subject?.hobbyId || '',
      // Exam fields
      examCategoryId: subject?.examCategoryId || '',
      examId: subject?.examId || '',
      examSubjectIds: subject?.examSubjectIds || [],
      // IT Course fields
      courseTypeId: subject?.courseTypeId || '',
      courseId: subject?.courseId || '',
    },
  });

  const watchedCategory = form.watch('serviceCategory');
  const watchedBoardId = form.watch('boardId');
  const watchedClassId = form.watch('classId');
  const watchedStreamId = form.watch('streamId');
  const watchedDegreeLevelId = form.watch('degreeLevelId');
  const watchedDegreeId = form.watch('degreeId');
  const watchedLanguageTypeId = form.watch('languageTypeId');
  const watchedHobbyTypeId = form.watch('hobbyTypeId');
  const watchedExamCategoryId = form.watch('examCategoryId');
  const watchedExamId = form.watch('examId');
  const watchedCourseTypeId = form.watch('courseTypeId');

  // Fetch data based on category
  const { data: boardsData } = useGetAllBoards({ limit: 100 }, { enabled: watchedCategory === 'school' });
  const { data: classesData } = useGetAllClasses({ board: watchedBoardId, limit: 100 }, { enabled: !!watchedBoardId });
  const { data: subjectsData } = useGetAllSubjects({ class: watchedClassId, limit: 100 }, { enabled: !!watchedClassId });

  const { data: streamsData } = useGetAllStreams({ limit: 100 }, { enabled: watchedCategory === 'college' });
  const { data: degreeLevelsData } = useGetAllDegreeLevels({ stream: watchedStreamId, limit: 100 }, { enabled: !!watchedStreamId });
  const { data: degreesData } = useGetAllDegrees({ degreeLevel: watchedDegreeLevelId, limit: 100 }, { enabled: !!watchedDegreeLevelId });
  const { data: branchesData } = useGetAllBranches({ degree: watchedDegreeId, limit: 100 }, { enabled: !!watchedDegreeId });
  const { data: collegeSubjectsData } = useGetAllCollegeSubjects({ degree: watchedDegreeId, limit: 100 }, { enabled: !!watchedDegreeId });

  const { data: languageTypesData } = useGetAllLanguageTypes({ limit: 100 }, { enabled: watchedCategory === 'language' });
  const { data: languagesData } = useGetAllLanguages({ languageType: watchedLanguageTypeId, limit: 100 }, { enabled: !!watchedLanguageTypeId });

  const { data: hobbyTypesData } = useGetAllHobbyTypes({ limit: 100 }, { enabled: watchedCategory === 'hobby' });
  const { data: hobbiesData } = useGetAllHobbies({ hobbyType: watchedHobbyTypeId, limit: 100 }, { enabled: !!watchedHobbyTypeId });

  const { data: examCategoriesData } = useGetAllExamCategories({ limit: 100 }, { enabled: watchedCategory === 'exam' });
  const { data: examsData } = useGetAllExams({ examCategory: watchedExamCategoryId, limit: 100 }, { enabled: !!watchedExamCategoryId });
  const { data: examSubjectsData } = useGetAllExamSubjects({ exam: watchedExamId, limit: 100 }, { enabled: !!watchedExamId });

  const { data: courseTypesData } = useGetAllCourseTypes({ limit: 100 }, { enabled: watchedCategory === 'it_courses' });
  const { data: coursesData } = useGetAllCourses({ courseType: watchedCourseTypeId, limit: 100 }, { enabled: !!watchedCourseTypeId });

  // Extract data from API responses
  const boards = boardsData?.data?.boards || [];
  const classes = classesData?.data?.classes || [];
  const subjects = subjectsData?.data?.subjects || [];
  const streams = streamsData?.data?.streams || [];
  const degreeLevels = degreeLevelsData?.data?.degreeLevels || [];
  const degrees = degreesData?.data?.degrees || [];
  const branches = branchesData?.data?.branches || [];
  const collegeSubjects = collegeSubjectsData?.data?.collegeSubjects || [];
  const languageTypes = languageTypesData?.data?.languageTypes || [];
  const languages = languagesData?.data?.languages || [];
  const hobbyTypes = hobbyTypesData?.data?.hobbyTypes || [];
  const hobbies = hobbiesData?.data?.hobbies || [];
  const examCategories = examCategoriesData?.data?.examCategories || [];
  const exams = examsData?.data?.exams || [];
  const examSubjects = examSubjectsData?.data?.examSubjects || [];
  const courseTypes = courseTypesData?.data?.courseTypes || [];
  const courses = coursesData?.data?.courses || [];

  const onSubmit = async (data: CreateTeachingSubjectInput) => {
    try {
      if (isEditing && subject) {
        await updateSubject.mutateAsync({
          id: subject._id,
          data,
        });
        toast.success('Teaching subject updated successfully!');
      } else {
        await createSubject.mutateAsync(data);
        toast.success('Teaching subject added successfully!');
      }
      onCancel?.();
    } catch (error: any) {
      toast.error(error?.response?.data?.message || 'Something went wrong!');
    }
  };

  const serviceCategoryOptions = Object.entries(serviceCategoryMap).map(([key, { label }]) => ({
    value: key,
    label,
  }));

  const currencyOptions = Object.entries(currencyMap).map(([key, { label, symbol }]) => ({
    value: key,
    label: `${symbol} ${label}`,
  }));

  const isLoading = createSubject.isPending || updateSubject.isPending;

  return (
    <div className='space-y-6'>
      <div className='text-center'>
        <h2 className='text-xl font-semibold text-gray-900'>{isEditing ? 'Update' : 'Add'} Teaching Subject</h2>
        <p className='text-sm text-gray-600 mt-1'>Add details about what you teach and your rates</p>
      </div>

      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className='space-y-6'>
          <div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
            {/* Service Category */}
            <PrimarySelect
              form={form}
              name='serviceCategory'
              label='Service Category'
              options={serviceCategoryOptions}
              placeholder='Select category'
              required
            />

            {/* Currency */}
            <PrimarySelect form={form} name='currency' label='Currency' options={currencyOptions} placeholder='Select currency' required />

            {/* Amount */}
            <PrimaryInput form={form} name='amount' label='Your Rate' placeholder='Enter your rate' type='number' required />

            {/* Budget */}
            <PrimaryInput form={form} name='budget' label='Minimum Budget' placeholder='Enter minimum budget' type='number' required />
          </div>

          {/* Dynamic fields based on category */}
          {watchedCategory === 'school' && (
            <div className='space-y-6'>
              <h3 className='text-lg font-medium text-gray-900 border-b pb-2'>School Details</h3>
              <div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
                <PrimarySelect
                  form={form}
                  name='boardId'
                  label='Board'
                  options={boards?.map((board) => ({ value: board._id, label: board.name })) || []}
                  placeholder='Select board'
                  required
                />

                <PrimarySelect
                  form={form}
                  name='classId'
                  label='Class'
                  options={classes?.map((cls) => ({ value: cls._id, label: cls.name })) || []}
                  placeholder='Select class'
                  required
                  disabled={!watchedBoardId}
                />

                <div className='md:col-span-2'>
                  <PrimaryMultiSelectForm
                    form={form}
                    name='subjectIds'
                    label='Subjects'
                    options={subjects?.map((subject) => ({ value: subject._id, label: subject.name })) || []}
                    placeholder='Select subjects'
                    required
                    disabled={!watchedClassId}
                  />
                </div>
              </div>
            </div>
          )}

          {watchedCategory === 'college' && (
            <div className='space-y-6'>
              <h3 className='text-lg font-medium text-gray-900 border-b pb-2'>College Details</h3>
              <div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
                <PrimarySelect
                  form={form}
                  name='streamId'
                  label='Stream'
                  options={streams?.map((stream) => ({ value: stream._id, label: stream.name })) || []}
                  placeholder='Select stream'
                  required
                />

                <PrimarySelect
                  form={form}
                  name='degreeLevelId'
                  label='Degree Level'
                  options={degreeLevels?.map((level) => ({ value: level._id, label: level.name })) || []}
                  placeholder='Select degree level'
                  required
                  disabled={!watchedStreamId}
                />

                <PrimarySelect
                  form={form}
                  name='degreeId'
                  label='Degree'
                  options={degrees?.map((degree) => ({ value: degree._id, label: degree.name })) || []}
                  placeholder='Select degree'
                  required
                  disabled={!watchedDegreeLevelId}
                />

                <PrimarySelect
                  form={form}
                  name='branchId'
                  label='Branch'
                  options={branches?.map((branch) => ({ value: branch._id, label: branch.name })) || []}
                  placeholder='Select branch'
                  required
                  disabled={!watchedDegreeId}
                />

                <div className='md:col-span-2'>
                  <PrimaryMultiSelectForm
                    form={form}
                    name='collegeSubjectIds'
                    label='Subjects'
                    options={collegeSubjects?.map((subject) => ({ value: subject._id, label: subject.name })) || []}
                    placeholder='Select subjects'
                    required
                    disabled={!watchedDegreeId}
                  />
                </div>
              </div>
            </div>
          )}

          {watchedCategory === 'language' && (
            <div className='space-y-6'>
              <h3 className='text-lg font-medium text-gray-900 border-b pb-2'>Language Details</h3>
              <div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
                <PrimarySelect
                  form={form}
                  name='languageTypeId'
                  label='Language Type'
                  options={languageTypes?.map((type) => ({ value: type._id, label: type.name })) || []}
                  placeholder='Select language type'
                  required
                />

                <PrimarySelect
                  form={form}
                  name='languageId'
                  label='Language'
                  options={languages?.map((lang) => ({ value: lang._id, label: lang.name })) || []}
                  placeholder='Select language'
                  required
                  disabled={!watchedLanguageTypeId}
                />
              </div>
            </div>
          )}

          {watchedCategory === 'hobby' && (
            <div className='space-y-6'>
              <h3 className='text-lg font-medium text-gray-900 border-b pb-2'>Hobby Details</h3>
              <div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
                <PrimarySelect
                  form={form}
                  name='hobbyTypeId'
                  label='Hobby Type'
                  options={hobbyTypes?.map((type) => ({ value: type._id, label: type.name })) || []}
                  placeholder='Select hobby type'
                  required
                />

                <PrimarySelect
                  form={form}
                  name='hobbyId'
                  label='Hobby'
                  options={hobbies?.map((hobby) => ({ value: hobby._id, label: hobby.name })) || []}
                  placeholder='Select hobby'
                  required
                  disabled={!watchedHobbyTypeId}
                />
              </div>
            </div>
          )}

          {watchedCategory === 'exam' && (
            <div className='space-y-6'>
              <h3 className='text-lg font-medium text-gray-900 border-b pb-2'>Exam Details</h3>
              <div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
                <PrimarySelect
                  form={form}
                  name='examCategoryId'
                  label='Exam Category'
                  options={examCategories?.map((cat) => ({ value: cat._id, label: cat.name })) || []}
                  placeholder='Select exam category'
                  required
                />

                <PrimarySelect
                  form={form}
                  name='examId'
                  label='Exam'
                  options={exams?.map((exam) => ({ value: exam._id, label: exam.name })) || []}
                  placeholder='Select exam'
                  required
                  disabled={!watchedExamCategoryId}
                />

                <div className='md:col-span-2'>
                  <PrimaryMultiSelectForm
                    form={form}
                    name='examSubjectIds'
                    label='Exam Subjects'
                    options={examSubjects?.map((subject) => ({ value: subject._id, label: subject.name })) || []}
                    placeholder='Select exam subjects'
                    required
                    disabled={!watchedExamId}
                  />
                </div>
              </div>
            </div>
          )}

          {watchedCategory === 'it_courses' && (
            <div className='space-y-6'>
              <h3 className='text-lg font-medium text-gray-900 border-b pb-2'>IT Course Details</h3>
              <div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
                <PrimarySelect
                  form={form}
                  name='courseTypeId'
                  label='Course Type'
                  options={courseTypes?.map((type) => ({ value: type._id, label: type.name })) || []}
                  placeholder='Select course type'
                  required
                />

                <PrimarySelect
                  form={form}
                  name='courseId'
                  label='Course'
                  options={courses?.map((course) => ({ value: course._id, label: course.name })) || []}
                  placeholder='Select course'
                  required
                  disabled={!watchedCourseTypeId}
                />
              </div>
            </div>
          )}

          <div className='flex gap-4 justify-end pt-4'>
            <CancelButton onClick={onCancel} disabled={isLoading} />
            <SubmitButton isLoading={isLoading} loadingText={isEditing ? 'Updating...' : 'Adding...'}>
              {isEditing ? 'Update Subject' : 'Add Subject'}
            </SubmitButton>
          </div>
        </form>
      </Form>
    </div>
  );
};

export default TeachingSubjectsForm;
