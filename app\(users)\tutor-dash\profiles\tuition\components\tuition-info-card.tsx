'use client';

import { Clock, MapPin, Languages, Users, BookOpen, Home } from 'lucide-react';
import { KeyValueDisplay } from '@/components/dashboard/shared/misc';
import { ITuitionInfoDocument } from '@/server/services/tutor/tuition-profile.service';
import { deliveryModeMap } from '@/validation/schemas/enquiry.maps';
import { languagesMap } from '@/constants';
import { Skeleton } from '@/components/ui/skeleton';

interface TuitionInfoCardProps {
  tuitionInfo: ITuitionInfoDocument | null | undefined;
  isLoading: boolean;
}

const TuitionInfoCard = ({ tuitionInfo, isLoading }: TuitionInfoCardProps) => {
  if (isLoading) {
    return (
      <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
        {Array.from({ length: 6 }).map((_, index) => (
          <div key={index} className='space-y-2'>
            <Skeleton className='h-4 w-24' />
            <Skeleton className='h-6 w-full' />
          </div>
        ))}
      </div>
    );
  }

  if (!tuitionInfo) {
    return (
      <div className='text-center py-12 bg-gray-50 rounded-lg border-2 border-dashed border-gray-200'>
        <BookOpen className='mx-auto h-12 w-12 text-gray-400 mb-4' />
        <h3 className='text-lg font-medium text-gray-900 mb-2'>No Tuition Information</h3>
        <p className='text-gray-600 mb-4'>
          You haven't added your tuition information yet. Click the edit button to get started.
        </p>
      </div>
    );
  }

  // Format delivery modes
  const deliveryModesDisplay = tuitionInfo.deliveryModes
    .map(mode => deliveryModeMap[mode]?.label || mode)
    .join(', ');

  // Format spoken languages
  const spokenLanguagesDisplay = tuitionInfo.spokenLanguages
    .map(lang => languagesMap[lang as keyof typeof languagesMap]?.label || lang)
    .join(', ');

  // Format experience
  const experienceYears = Math.floor(tuitionInfo.totalTeachingExperience / 12);
  const experienceMonths = tuitionInfo.totalTeachingExperience % 12;
  const experienceDisplay = experienceYears > 0 
    ? `${experienceYears} year${experienceYears > 1 ? 's' : ''} ${experienceMonths > 0 ? `${experienceMonths} month${experienceMonths > 1 ? 's' : ''}` : ''}`
    : `${experienceMonths} month${experienceMonths > 1 ? 's' : ''}`;

  return (
    <div className='space-y-6'>
      {/* Main Information Grid */}
      <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
        <KeyValueDisplay 
          label='Teaching Experience' 
          value={experienceDisplay.trim()}
          icon={<Clock className='w-4 h-4 text-primaryColor' />}
        />
        
        <KeyValueDisplay 
          label='Teacher Type' 
          value={tuitionInfo.isFullTimeTeacher ? 'Full-time Teacher' : 'Part-time Teacher'}
          icon={<Users className='w-4 h-4 text-primaryColor' />}
        />
        
        <KeyValueDisplay 
          label='Special Students' 
          value={tuitionInfo.teachesSpecialStudents ? 'Yes, I teach special students' : 'No special students'}
          icon={<BookOpen className='w-4 h-4 text-primaryColor' />}
        />
        
        <KeyValueDisplay 
          label='Travel Distance' 
          value={`Up to ${tuitionInfo.maxTravelDistance} km`}
          icon={<MapPin className='w-4 h-4 text-primaryColor' />}
        />
        
        <KeyValueDisplay 
          label='Teaching Modes' 
          value={deliveryModesDisplay}
          icon={<Home className='w-4 h-4 text-primaryColor' />}
        />
        
        <KeyValueDisplay 
          label='Languages' 
          value={spokenLanguagesDisplay}
          icon={<Languages className='w-4 h-4 text-primaryColor' />}
        />
        
        <div className='md:col-span-2'>
          <KeyValueDisplay 
            label='Location' 
            value={tuitionInfo.location}
            icon={<MapPin className='w-4 h-4 text-primaryColor' />}
          />
        </div>
      </div>

      {/* Description Section */}
      <div className='bg-gray-50 rounded-lg p-4'>
        <h4 className='font-medium text-gray-900 mb-2 flex items-center gap-2'>
          <BookOpen className='w-4 h-4 text-primaryColor' />
          About Me
        </h4>
        <p className='text-gray-700 leading-relaxed'>
          {tuitionInfo.description}
        </p>
      </div>

      {/* Quick Stats */}
      <div className='grid grid-cols-2 md:grid-cols-4 gap-4'>
        <div className='bg-primaryColor-50 rounded-lg p-4 text-center'>
          <div className='text-2xl font-bold text-primaryColor mb-1'>
            {tuitionInfo.totalTeachingExperience}
          </div>
          <div className='text-sm text-primaryColor-700'>Months Experience</div>
        </div>
        
        <div className='bg-blue-50 rounded-lg p-4 text-center'>
          <div className='text-2xl font-bold text-blue-600 mb-1'>
            {tuitionInfo.deliveryModes.length}
          </div>
          <div className='text-sm text-blue-700'>Teaching Modes</div>
        </div>
        
        <div className='bg-green-50 rounded-lg p-4 text-center'>
          <div className='text-2xl font-bold text-green-600 mb-1'>
            {tuitionInfo.spokenLanguages.length}
          </div>
          <div className='text-sm text-green-700'>Languages</div>
        </div>
        
        <div className='bg-purple-50 rounded-lg p-4 text-center'>
          <div className='text-2xl font-bold text-purple-600 mb-1'>
            {tuitionInfo.maxTravelDistance}
          </div>
          <div className='text-sm text-purple-700'>Max Distance (km)</div>
        </div>
      </div>
    </div>
  );
};

export default TuitionInfoCard;
