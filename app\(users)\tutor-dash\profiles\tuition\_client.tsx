'use client';

import { useState } from 'react';
import { Clock, Edit2, Home, MapPin, PencilOff, School, X } from 'lucide-react';
import { TabsFilterWithBadge, TabsMobileFilterWithBadge } from '@/components/dashboard/shared/misc/TabsFilterWithBadge';
import { serviceCategoryMap } from '@/validation/schemas/education/index.maps';
import { KeyValueDisplay } from '@/components/dashboard/shared/misc';
import { AlertDialog, AlertDialogCancel, AlertDialogContent, AlertDialogTrigger } from '@/components/ui/alert-dialog';

// Import new components
import TuitionInfoForm from './components/tuition-info-form';
import TuitionInfoCard from './components/tuition-info-card';
import TeachingExperienceForm from './components/teaching-experience-form';
import TeachingExperienceList from './components/teaching-experience-list';
import TeachingSubjectsForm from './components/teaching-subjects-form';
import TeachingSubjectsList from './components/teaching-subjects-list';

// Import hooks
import { useGetTuitionInfo, useGetAllTeachingExperience, useGetAllTeachingSubjects } from '@/hooks/tutor/tuition-profile.hooks';

const TutorTuitionProfileClient = () => {
  const [editTuitionInfo, setEditTuitionInfo] = useState(false);
  const [selectedMainTab, setSelectedMainTab] = useState('info');
  const [selectedSubTab, setSelectedSubTab] = useState(serviceCategoryMap.schools.key);

  // Fetch data using hooks
  const { data: tuitionInfo, isLoading: tuitionInfoLoading } = useGetTuitionInfo();
  const { data: teachingExperience, isLoading: experienceLoading } = useGetAllTeachingExperience();
  const { data: teachingSubjects, isLoading: subjectsLoading } = useGetAllTeachingSubjects();

  const toggleEditItem = (setItem: React.Dispatch<React.SetStateAction<boolean>>) => {
    setItem((prevValue) => !prevValue);
  };

  const mainTabCounts = { 
    info: tuitionInfo ? 1 : 0, 
    experience: teachingExperience?.length || 0, 
    'subjects-classes': teachingSubjects?.length || 0 
  };

  const subTabCounts = Object.entries(serviceCategoryMap).reduce((acc, [key, { key: tabKey }]) => {
    const count = teachingSubjects?.filter(subject => subject.serviceCategory === tabKey).length || 0;
    acc[tabKey] = count;
    return acc;
  }, {} as { [key: string]: number });

  const mainTabLabels = {
    large: {
      info: 'Tuition Information',
      experience: 'Teaching Experience',
      'subjects-classes': 'What Do You Teach',
    },
    mobile: {
      info: 'Tuition Info',
      experience: 'Experience',
      'subjects-classes': 'What Do You Teach',
    },
  };

  const isSecondLevelVisible = selectedMainTab === 'subjects-classes';

  return (
    <div className='bg-white p-6 rounded-3xl relative lg:min-h-[650px] flex md:flex-wrap lg:flex-nowrap gap-6 lg:gap-12 items-start'>
      <div className='flex flex-col gap-6 max-md:hidden max-lg:flex-row max-lg:w-full'>
        <TabsFilterWithBadge
          selectedTab={selectedMainTab}
          setSelectedTab={setSelectedMainTab}
          tabCounts={mainTabCounts}
          labels={mainTabLabels.large}
        />
        {isSecondLevelVisible && (
          <div className='md:w-full lg:w-auto'>
            <TabsFilterWithBadge
              displayInFullWidth
              selectedTab={selectedSubTab}
              setSelectedTab={setSelectedSubTab}
              secondLevelLabels={serviceCategoryMap}
              tabCounts={subTabCounts}
              showNumbers
            />
          </div>
        )}
      </div>
      
      <div className='w-full'>
        {selectedMainTab === 'info' && (
          <div className='flex flex-col gap-6 w-full'>
            <div className='flex justify-between items-center'>
              <h2 className='text-base font-semibold'>Tuition Information</h2>
              <button
                onClick={() => toggleEditItem(setEditTuitionInfo)}
                className='py-2 px-3 bg-primaryColor-50 text-primaryColor rounded transition-all duration-300'
              >
                {editTuitionInfo ? <PencilOff size={16} /> : <Edit2 size={16} />}
              </button>
            </div>
            {editTuitionInfo ? (
              <TuitionInfoForm onCancel={() => setEditTuitionInfo(false)} />
            ) : (
              <TuitionInfoCard tuitionInfo={tuitionInfo} isLoading={tuitionInfoLoading} />
            )}
          </div>
        )}
        
        {selectedMainTab === 'experience' && (
          <div>
            <div className='flex justify-end items-start mb-4'>
              <AlertDialog>
                <div className='flex justify-between items-center w-full flex-wrap gap-2'>
                  <h2 className='text-base font-semibold'>Teaching Experience Info</h2>
                  <AlertDialogTrigger className='btn-default-sm'>Add Experience</AlertDialogTrigger>
                </div>

                <AlertDialogContent className='lg:w-[700px] lg:max-w-none'>
                  <TeachingExperienceForm />
                  <AlertDialogCancel className='border-0 absolute rounded-none rounded-tr-md right-0 top-0 bg-primaryColor-50 hover:bg-primaryColor-100 flex-shrink-0 p-2 tracking-wider cursor-pointer group overflow-hidden'>
                    <p className='text-primaryColor'>
                      <X />
                    </p>
                  </AlertDialogCancel>
                </AlertDialogContent>
              </AlertDialog>
            </div>
            <TeachingExperienceList experiences={teachingExperience} isLoading={experienceLoading} />
          </div>
        )}
        
        {selectedMainTab === 'subjects-classes' && (
          <div>
            <div className='flex justify-end items-start mb-4'>
              <AlertDialog>
                <div className='flex justify-between items-center w-full'>
                  <h2 className='text-base font-semibold'>
                    {serviceCategoryMap[selectedSubTab as keyof typeof serviceCategoryMap]?.label} Section Information
                  </h2>
                  <AlertDialogTrigger className='btn-default-sm'>Add Record</AlertDialogTrigger>
                </div>
                <AlertDialogContent className='!w-[700px] !max-w-none'>
                  <TeachingSubjectsForm selectedCategory={selectedSubTab} />
                  <AlertDialogCancel className='border-0 absolute rounded-none rounded-tr-md right-0 top-0 bg-primaryColor-50 hover:bg-primaryColor-100 flex-shrink-0 p-2 tracking-wider cursor-pointer group overflow-hidden'>
                    <p className='text-primaryColor'>
                      <X />
                    </p>
                  </AlertDialogCancel>
                </AlertDialogContent>
              </AlertDialog>
            </div>
            <TeachingSubjectsList 
              subjects={teachingSubjects} 
              selectedCategory={selectedSubTab}
              isLoading={subjectsLoading} 
            />
          </div>
        )}
      </div>
      
      <TabsMobileFilterWithBadge
        selectedTab={selectedMainTab}
        setSelectedTab={setSelectedMainTab}
        tabCounts={mainTabCounts}
        showNumbers={false}
        labels={mainTabLabels.mobile}
        showSecondLevel={isSecondLevelVisible}
        secondLevelTabCounts={subTabCounts}
        secondLevelLabels={serviceCategoryMap}
        selectedSecondTab={selectedSubTab}
        secondLevelShowNumbers={true}
        setSelectedSecondTab={setSelectedSubTab}
      />
    </div>
  );
};

export default TutorTuitionProfileClient;
