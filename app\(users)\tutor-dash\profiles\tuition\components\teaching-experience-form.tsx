'use client';

import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { toast } from 'react-toastify';
import { Form } from '@/components/ui/form';
import { useState, useEffect } from 'react';
import { 
  PrimaryInput, 
  PrimarySelect, 
  PrimaryAutoCompleteInput,
  SubmitButton,
  CancelButton 
} from '@/components/forms';
import { createTeachingExperienceSchema, CreateTeachingExperienceInput } from '@/validation/schemas/tutor/profiles/teaching-experience.schema';
import { useCreateTeachingExperience, useUpdateTeachingExperience } from '@/hooks/tutor/tuition-profile.hooks';
import { tuitionTypeMap } from '@/validation/schemas/tutor/tuition.maps';
import { ITeachingExperienceDocument } from '@/server/services/tutor/tuition-profile.service';

interface TeachingExperienceFormProps {
  experience?: ITeachingExperienceDocument;
  onCancel?: () => void;
}

const TeachingExperienceForm = ({ experience, onCancel }: TeachingExperienceFormProps) => {
  const [hideNameLocation, setHideNameLocation] = useState(false);
  const createExperience = useCreateTeachingExperience();
  const updateExperience = useUpdateTeachingExperience();

  const isEditing = !!experience;

  const form = useForm<CreateTeachingExperienceInput>({
    resolver: zodResolver(createTeachingExperienceSchema),
    defaultValues: {
      tuitionType: experience?.tuitionType || 'private',
      experienceMonths: experience?.experienceMonths || 0,
      placeName: experience?.placeName || 'Self',
      location: experience?.location || 'Self',
      businessLocationId: experience?.businessLocationId || undefined,
    },
  });

  const watchedTuitionType = form.watch('tuitionType');

  useEffect(() => {
    if (watchedTuitionType === 'private') {
      form.setValue('placeName', 'Self');
      form.setValue('location', 'Self');
      setHideNameLocation(true);
    } else {
      if (!isEditing) {
        form.setValue('placeName', '');
        form.setValue('location', '');
      }
      setHideNameLocation(false);
    }
  }, [watchedTuitionType, form, isEditing]);

  const onSubmit = async (data: CreateTeachingExperienceInput) => {
    try {
      if (isEditing && experience) {
        await updateExperience.mutateAsync({
          id: experience._id,
          data,
        });
        toast.success('Teaching experience updated successfully!');
      } else {
        await createExperience.mutateAsync(data);
        toast.success('Teaching experience added successfully!');
      }
      onCancel?.();
    } catch (error: any) {
      toast.error(error?.response?.data?.message || 'Something went wrong!');
    }
  };

  const tuitionTypeOptions = Object.entries(tuitionTypeMap).map(([key, { label }]) => ({
    value: key,
    label,
  }));

  // Mock business location suggestions - replace with actual API call
  const businessLocationSuggestions = [
    { value: '1', label: 'Delhi Public School - New Delhi' },
    { value: '2', label: 'Kendriya Vidyalaya - Mumbai' },
    { value: '3', label: 'St. Xavier\'s College - Kolkata' },
    { value: '4', label: 'Miranda House - Delhi University' },
    { value: '5', label: 'IIT Coaching Institute - Kota' },
  ];

  const isLoading = createExperience.isPending || updateExperience.isPending;

  return (
    <div className='space-y-6'>
      <div className='text-center'>
        <h2 className='text-xl font-semibold text-gray-900'>
          {isEditing ? 'Update' : 'Add'} Teaching Experience
        </h2>
        <p className='text-sm text-gray-600 mt-1'>
          Add details about your teaching experience
        </p>
      </div>

      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className='space-y-6'>
          <div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
            {/* Tuition Type */}
            <PrimarySelect
              form={form}
              name='tuitionType'
              label='Tuition Type'
              options={tuitionTypeOptions}
              placeholder='Select tuition type'
              required
            />

            {/* Experience in Months */}
            <PrimaryInput
              form={form}
              name='experienceMonths'
              label='Experience (Months)'
              placeholder='Enter experience in months'
              type='number'
              required
            />

            {/* Place Name - Hidden for private tuition */}
            {!hideNameLocation && (
              <div className='md:col-span-2'>
                <PrimaryAutoCompleteInput
                  form={form}
                  name='placeName'
                  label='Institution/Place Name'
                  placeholder='Search for institution or enter manually'
                  suggestions={businessLocationSuggestions}
                  required
                  onSelect={(selected) => {
                    form.setValue('businessLocationId', selected.value);
                    form.setValue('placeName', selected.label.split(' - ')[0]);
                    form.setValue('location', selected.label.split(' - ')[1] || '');
                  }}
                />
              </div>
            )}

            {/* Location - Hidden for private tuition */}
            {!hideNameLocation && (
              <div className='md:col-span-2'>
                <PrimaryInput
                  form={form}
                  name='location'
                  label='Location'
                  placeholder='Enter location'
                  required
                />
              </div>
            )}

            {/* Show info for private tuition */}
            {hideNameLocation && (
              <div className='md:col-span-2 bg-blue-50 border border-blue-200 rounded-lg p-4'>
                <p className='text-blue-800 text-sm'>
                  <strong>Private Tuition:</strong> Place name and location are automatically set to "Self" 
                  since you provide tuition independently.
                </p>
              </div>
            )}
          </div>

          <div className='flex gap-4 justify-end pt-4'>
            <CancelButton onClick={onCancel} disabled={isLoading} />
            <SubmitButton 
              isLoading={isLoading}
              loadingText={isEditing ? 'Updating...' : 'Adding...'}
            >
              {isEditing ? 'Update Experience' : 'Add Experience'}
            </SubmitButton>
          </div>
        </form>
      </Form>
    </div>
  );
};

export default TeachingExperienceForm;
