import type { Metada<PERSON> } from 'next';
import { Inter } from 'next/font/google';
import '@/app/globals.css';
import { NavBar, SideBar } from '@/components/dashboard/tutor-dash/misc';
import BottomNavBar from '@/components/dashboard/tutor-dash/misc/MobileBottomBar';
import ToastProvider from '@/components/providers/ToastProvider';
import QueryProvider from '@/lib/react-query/QueryProvider';

const inter = Inter({ subsets: ['latin'] });

export const metadata: Metadata = {
  title: 'Tutor Dashboard',
  description: 'Generated by create next app',
};

export default function RootLayout({ children }: Readonly<{ children: React.ReactNode }>) {
  return (
    <html lang='en'>
      <body className={inter.className}>
        <QueryProvider>
          <div className='flex items-start justify-start bg-slate-100 min-h-screen p-4 lg:gap-4'>
            <div className='shrink-0'>
              <SideBar />
            </div>
            <div className='flex flex-col gap-4 flex-1 max-w-full w-full'>
              <NavBar />
              <main className='flex-1 rounded-3xl max-w-full w-full'>{children}</main>
            </div>
          </div>
          <ToastProvider />
        </QueryProvider>
      </body>
    </html>
  );
}
