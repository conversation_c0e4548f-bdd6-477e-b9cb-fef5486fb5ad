'use client';

import { useState } from 'react';
import { Plus, Search } from 'lucide-react';
import LeadCard from './lead-card';
import { IEnquiryDocument } from '@/server/services/enquiry.service';
import AddEnquiryModal from './lead-add-modal';
import LeadViewModal from './lead-view-modal';
import LeadCloseModal from './lead-close-modal';
import { Backgrounds } from '@/components/dashboard/shared/misc';

interface LeadListProps {
  enquiries: IEnquiryDocument[];
  selectedTab: 'active' | 'converted' | 'closed';
}

const LeadList = ({ enquiries, selectedTab }: LeadListProps) => {
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [selectedEnquiry, setSelectedEnquiry] = useState<IEnquiryDocument | null>(null);
  const [isViewModalOpen, setIsViewModalOpen] = useState(false);
  const [isCloseModalOpen, setIsCloseModalOpen] = useState(false);

  const handleViewLead = (enquiry: IEnquiryDocument) => {
    setSelectedEnquiry(enquiry);
    setIsViewModalOpen(true);
  };

  const handleCloseLead = (enquiry: IEnquiryDocument) => {
    setSelectedEnquiry(enquiry);
    setIsCloseModalOpen(true);
  };

  const handleAddNewLead = () => {
    setIsAddModalOpen(true);
  };

  return (
    <>
      <div className='lg:w-3/4 w-full'>
        <div className='bg-white rounded-2xl p-6 border border-gray-100 shadow-sm mb-8 relative overflow-hidden'>
          <Backgrounds variant='primary' />
          <div className='relative z-10 flex flex-col md:flex-row justify-between items-start md:items-center'>
            <div>
              <div className='flex items-center gap-2'>
                <h2 className='text-2xl font-bold text-gray-800 capitalize'>{selectedTab} Enquiries</h2>
                <span className='px-3 py-1 bg-blue-50 text-blue-600 rounded-full text-xs font-medium'>
                  {enquiries.length} {enquiries.length === 1 ? 'enquiry' : 'enquiries'}
                </span>
              </div>
              <p className='text-sm text-gray-500 mt-1'>View and manage your {selectedTab.toLowerCase()} tuition requests</p>
            </div>

            <button
              onClick={handleAddNewLead}
              className='mt-4 md:mt-0 flex items-center gap-2 px-5 py-2.5 bg-gradient-to-r from-red-500 to-red-600 text-white rounded-lg font-medium hover:from-red-600 hover:to-red-700 transition-all duration-300 shadow-md hover:shadow-lg hover:scale-[1.02]'
            >
              <Plus size={18} />
              <span>Add New Enquiry</span>
            </button>
          </div>
        </div>

        {/* Lead cards grid */}
        {enquiries.length > 0 ? (
          <div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
            {enquiries.map((enquiry) => (
              <LeadCard key={enquiry._id} enquiry={enquiry} onView={handleViewLead} onClose={handleCloseLead} />
            ))}
          </div>
        ) : (
          <div className='flex flex-col items-center justify-center py-12 text-center bg-white rounded-xl border border-gray-100 shadow-sm relative overflow-hidden'>
            <div className='absolute top-0 inset-x-0 h-2 bg-gradient-to-r from-red-400 to-red-600'></div>
            <div className='absolute -right-12 -top-12 w-48 h-48 bg-red-50 rounded-full opacity-50'></div>
            <div className='absolute -left-12 -bottom-12 w-48 h-48 bg-blue-50 rounded-full opacity-50'></div>

            <div className='w-24 h-24 rounded-full bg-gradient-to-br from-red-50 to-red-100 flex items-center justify-center mb-6 shadow-inner'>
              <Search className='text-red-500' size={40} />
            </div>

            <h4 className='text-2xl font-bold text-gray-800 mb-2'>No {selectedTab} enquiries found</h4>
            <p className='text-gray-500 max-w-md mb-8'>
              You don't have any {selectedTab} enquiries at the moment. Create a new enquiry to get started.
            </p>

            <button
              onClick={handleAddNewLead}
              className='flex items-center gap-2 px-7 py-3.5 bg-gradient-to-r from-red-500 to-red-600 text-white rounded-lg font-semibold hover:from-red-600 hover:to-red-700 transition-all duration-300 shadow-lg hover:shadow-xl hover:scale-[1.03] transform-gpu'
            >
              <Plus size={20} className='text-white' />
              <span>Create New Enquiry</span>
            </button>
          </div>
        )}
      </div>

      {/* Add Enquiry Modal */}
      <AddEnquiryModal isOpen={isAddModalOpen} onClose={() => setIsAddModalOpen(false)} />

      {/* View Enquiry Modal */}
      <LeadViewModal isOpen={isViewModalOpen} onClose={() => setIsViewModalOpen(false)} enquiry={selectedEnquiry} />

      {/* Close Enquiry Modal */}
      <LeadCloseModal isOpen={isCloseModalOpen} onClose={() => setIsCloseModalOpen(false)} enquiry={selectedEnquiry} />
    </>
  );
};

export default LeadList;
