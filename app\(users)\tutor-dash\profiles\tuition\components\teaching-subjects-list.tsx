'use client';

import { useState } from 'react';
import { BookOpen, Edit, Trash2, DollarSign, GraduationCap } from 'lucide-react';
import { SimpleTable } from '@/components/dashboard/tutor-dash/misc';
import { ITeachingSubjectDocument } from '@/server/services/tutor/tuition-profile.service';
import { serviceCategoryMap } from '@/validation/schemas/education/index.maps';
import { currencyMap } from '@/validation/schemas/tutor/tuition.maps';
import { useDeleteTeachingSubject } from '@/hooks/tutor/tuition-profile.hooks';
import { toast } from 'react-toastify';
import { Skeleton } from '@/components/ui/skeleton';
import TeachingSubjectsForm from './teaching-subjects-form';
import { PrimaryModalWithHeader } from '@/components/dashboard/shared/misc';

interface TeachingSubjectsListProps {
  subjects: ITeachingSubjectDocument[] | undefined;
  selectedCategory: string;
  isLoading: boolean;
}

const TeachingSubjectsList = ({ subjects, selectedCategory, isLoading }: TeachingSubjectsListProps) => {
  const [editingSubject, setEditingSubject] = useState<ITeachingSubjectDocument | null>(null);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const deleteSubject = useDeleteTeachingSubject();

  const handleEdit = (subject: ITeachingSubjectDocument) => {
    setEditingSubject(subject);
    setIsEditModalOpen(true);
  };

  const handleDelete = async (id: string) => {
    try {
      await deleteSubject.mutateAsync(id);
      toast.success('Teaching subject deleted successfully!');
    } catch (error: any) {
      toast.error(error?.response?.data?.message || 'Failed to delete subject');
    }
  };

  const closeEditModal = () => {
    setIsEditModalOpen(false);
    setEditingSubject(null);
  };

  // Filter subjects by selected category
  const filteredSubjects = subjects?.filter(subject => subject.serviceCategory === selectedCategory) || [];

  if (isLoading) {
    return (
      <div className='space-y-4'>
        {Array.from({ length: 3 }).map((_, index) => (
          <div key={index} className='border rounded-lg p-4'>
            <div className='grid grid-cols-1 md:grid-cols-5 gap-4'>
              <Skeleton className='h-6 w-full' />
              <Skeleton className='h-6 w-full' />
              <Skeleton className='h-6 w-full' />
              <Skeleton className='h-6 w-full' />
              <Skeleton className='h-6 w-full' />
            </div>
          </div>
        ))}
      </div>
    );
  }

  if (filteredSubjects.length === 0) {
    return (
      <div className='text-center py-12 bg-gray-50 rounded-lg border-2 border-dashed border-gray-200'>
        <BookOpen className='mx-auto h-12 w-12 text-gray-400 mb-4' />
        <h3 className='text-lg font-medium text-gray-900 mb-2'>
          No {serviceCategoryMap[selectedCategory as keyof typeof serviceCategoryMap]?.label} Subjects
        </h3>
        <p className='text-gray-600 mb-4'>
          You haven't added any subjects for this category yet. Click "Add Record" to get started.
        </p>
      </div>
    );
  }

  // Helper function to get subject display based on category
  const getSubjectDisplay = (subject: ITeachingSubjectDocument) => {
    switch (subject.serviceCategory) {
      case 'school':
        return {
          category: subject.boardDetails?.name || 'Unknown Board',
          level: subject.classDetails?.name || 'Unknown Class',
          subjects: subject.subjectDetails?.map(s => s.name).join(', ') || 'No subjects',
        };
      case 'college':
        return {
          category: subject.streamDetails?.name || 'Unknown Stream',
          level: `${subject.degreeLevelDetails?.name} - ${subject.degreeDetails?.name}`,
          subjects: subject.collegeSubjectDetails?.map(s => s.name).join(', ') || 'No subjects',
        };
      case 'language':
        return {
          category: subject.languageTypeDetails?.name || 'Unknown Type',
          level: subject.languageDetails?.name || 'Unknown Language',
          subjects: 'Language Teaching',
        };
      case 'hobby':
        return {
          category: subject.hobbyTypeDetails?.name || 'Unknown Type',
          level: subject.hobbyDetails?.name || 'Unknown Hobby',
          subjects: 'Hobby Teaching',
        };
      case 'exam':
        return {
          category: subject.examCategoryDetails?.name || 'Unknown Category',
          level: subject.examDetails?.name || 'Unknown Exam',
          subjects: subject.examSubjectDetails?.map(s => s.name).join(', ') || 'No subjects',
        };
      case 'it_courses':
        return {
          category: subject.courseTypeDetails?.name || 'Unknown Type',
          level: subject.courseDetails?.name || 'Unknown Course',
          subjects: 'IT Course Teaching',
        };
      default:
        return {
          category: 'Unknown',
          level: 'Unknown',
          subjects: 'Unknown',
        };
    }
  };

  // Prepare data for table
  const tableData = filteredSubjects.map((subject) => {
    const display = getSubjectDisplay(subject);
    const currency = currencyMap[subject.currency as keyof typeof currencyMap];
    
    return [
      display.category,
      display.level,
      display.subjects,
      `${currency?.symbol || ''}${subject.amount}/${currency?.label || subject.currency}`,
      <div key={subject._id} className='flex gap-2'>
        <button
          onClick={() => handleEdit(subject)}
          className='p-2 text-blue-600 hover:bg-blue-50 rounded-lg transition-colors'
          title='Edit'
        >
          <Edit className='w-4 h-4' />
        </button>
        <button
          onClick={() => handleDelete(subject._id)}
          className='p-2 text-red-600 hover:bg-red-50 rounded-lg transition-colors'
          title='Delete'
          disabled={deleteSubject.isPending}
        >
          <Trash2 className='w-4 h-4' />
        </button>
      </div>
    ];
  });

  // Dynamic headers based on category
  const getHeaders = () => {
    switch (selectedCategory) {
      case 'school':
        return ['Board', 'Class', 'Subjects', 'Rate', 'Actions'];
      case 'college':
        return ['Stream', 'Degree', 'Subjects', 'Rate', 'Actions'];
      case 'language':
        return ['Type', 'Language', 'Teaching', 'Rate', 'Actions'];
      case 'hobby':
        return ['Type', 'Hobby', 'Teaching', 'Rate', 'Actions'];
      case 'exam':
        return ['Category', 'Exam', 'Subjects', 'Rate', 'Actions'];
      case 'it_courses':
        return ['Type', 'Course', 'Teaching', 'Rate', 'Actions'];
      default:
        return ['Category', 'Level', 'Subjects', 'Rate', 'Actions'];
    }
  };

  return (
    <>
      {/* Desktop Table View */}
      <div className='max-md:hidden'>
        <SimpleTable
          headers={getHeaders()}
          rows={tableData}
        />
      </div>

      {/* Mobile Card View */}
      <div className='md:hidden space-y-4'>
        {filteredSubjects.map((subject) => {
          const display = getSubjectDisplay(subject);
          const currency = currencyMap[subject.currency as keyof typeof currencyMap];
          
          return (
            <div key={subject._id} className='border rounded-lg p-4 bg-white shadow-sm'>
              <div className='space-y-3'>
                <div className='flex items-center gap-2'>
                  <GraduationCap strokeWidth={1.5} className='text-primaryColor w-4 h-4' />
                  <span className='font-medium text-sm'>Category:</span>
                  <span className='text-gray-600 text-sm'>{display.category}</span>
                </div>
                
                <div className='flex items-center gap-2'>
                  <BookOpen strokeWidth={1.5} className='text-primaryColor w-4 h-4' />
                  <span className='font-medium text-sm'>Level:</span>
                  <span className='text-gray-600 text-sm'>{display.level}</span>
                </div>
                
                <div className='flex items-center gap-2'>
                  <BookOpen strokeWidth={1.5} className='text-primaryColor w-4 h-4' />
                  <span className='font-medium text-sm'>Subjects:</span>
                  <span className='text-gray-600 text-sm'>{display.subjects}</span>
                </div>
                
                <div className='flex items-center gap-2'>
                  <DollarSign strokeWidth={1.5} className='text-primaryColor w-4 h-4' />
                  <span className='font-medium text-sm'>Rate:</span>
                  <span className='text-gray-600 text-sm'>
                    {currency?.symbol || ''}{subject.amount}/{currency?.label || subject.currency}
                  </span>
                </div>
                
                <div className='flex justify-end pt-2 border-t'>
                  <div className='flex gap-2'>
                    <button
                      onClick={() => handleEdit(subject)}
                      className='p-2 text-blue-600 hover:bg-blue-50 rounded-lg transition-colors'
                      title='Edit'
                    >
                      <Edit className='w-4 h-4' />
                    </button>
                    <button
                      onClick={() => handleDelete(subject._id)}
                      className='p-2 text-red-600 hover:bg-red-50 rounded-lg transition-colors'
                      title='Delete'
                      disabled={deleteSubject.isPending}
                    >
                      <Trash2 className='w-4 h-4' />
                    </button>
                  </div>
                </div>
              </div>
            </div>
          );
        })}
      </div>

      {/* Edit Modal */}
      <PrimaryModalWithHeader
        isOpen={isEditModalOpen}
        onClose={closeEditModal}
        title='Edit Teaching Subject'
        maxWidth='max-w-4xl'
      >
        {editingSubject && (
          <TeachingSubjectsForm
            subject={editingSubject}
            selectedCategory={selectedCategory}
            onCancel={closeEditModal}
          />
        )}
      </PrimaryModalWithHeader>
    </>
  );
};

export default TeachingSubjectsList;
