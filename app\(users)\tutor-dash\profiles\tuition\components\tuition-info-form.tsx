'use client';

import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { toast } from 'react-toastify';
import { Form } from '@/components/ui/form';
import { 
  PrimaryInput, 
  PrimarySelect, 
  PrimaryTextarea, 
  PrimarySwitchInput,
  PrimaryMultiSelectForm,
  SubmitButton,
  CancelButton 
} from '@/components/forms';
import { createTuitionInfoSchema, CreateTuitionInfoInput } from '@/validation/schemas/tutor/profiles/tuition-info.schema';
import { useCreateTuitionInfo, useUpdateTuitionInfo, useGetTuitionInfo } from '@/hooks/tutor/tuition-profile.hooks';
import { deliveryModeMap, deliveryModeOptions } from '@/validation/schemas/enquiry.maps';
import { languagesMap } from '@/constants';

interface TuitionInfoFormProps {
  onCancel?: () => void;
}

const TuitionInfoForm = ({ onCancel }: TuitionInfoFormProps) => {
  const { data: existingTuitionInfo } = useGetTuitionInfo();
  const createTuitionInfo = useCreateTuitionInfo();
  const updateTuitionInfo = useUpdateTuitionInfo();

  const isEditing = !!existingTuitionInfo;

  const form = useForm<CreateTuitionInfoInput>({
    resolver: zodResolver(createTuitionInfoSchema),
    defaultValues: {
      totalTeachingExperience: existingTuitionInfo?.totalTeachingExperience || 0,
      isFullTimeTeacher: existingTuitionInfo?.isFullTimeTeacher || false,
      teachesSpecialStudents: existingTuitionInfo?.teachesSpecialStudents || false,
      maxTravelDistance: existingTuitionInfo?.maxTravelDistance || 0,
      spokenLanguages: existingTuitionInfo?.spokenLanguages || [],
      deliveryModes: existingTuitionInfo?.deliveryModes || [],
      location: existingTuitionInfo?.location || '',
      description: existingTuitionInfo?.description || '',
    },
  });

  const onSubmit = async (data: CreateTuitionInfoInput) => {
    try {
      if (isEditing && existingTuitionInfo) {
        await updateTuitionInfo.mutateAsync({
          id: existingTuitionInfo._id,
          data,
        });
        toast.success('Tuition information updated successfully!');
      } else {
        await createTuitionInfo.mutateAsync(data);
        toast.success('Tuition information created successfully!');
      }
      onCancel?.();
    } catch (error: any) {
      toast.error(error?.response?.data?.message || 'Something went wrong!');
    }
  };

  const languageOptions = Object.entries(languagesMap).map(([key, { label }]) => ({
    value: key,
    label,
  }));

  const isLoading = createTuitionInfo.isPending || updateTuitionInfo.isPending;

  return (
    <div className='space-y-6'>
      <div className='text-center'>
        <h2 className='text-xl font-semibold text-gray-900'>
          {isEditing ? 'Update' : 'Add'} Tuition Information
        </h2>
        <p className='text-sm text-gray-600 mt-1'>
          Provide your teaching experience and preferences
        </p>
      </div>

      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className='space-y-6'>
          <div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
            {/* Total Teaching Experience */}
            <PrimaryInput
              form={form}
              name='totalTeachingExperience'
              label='Total Teaching Experience (Months)'
              placeholder='Enter experience in months'
              type='number'
              required
            />

            {/* Max Travel Distance */}
            <PrimaryInput
              form={form}
              name='maxTravelDistance'
              label='Maximum Travel Distance (KM)'
              placeholder='Enter distance in kilometers'
              type='number'
              required
            />

            {/* Full Time Teacher Switch */}
            <div className='md:col-span-2'>
              <PrimarySwitchInput
                form={form}
                name='isFullTimeTeacher'
                label='Are you a full-time teacher?'
                description='Toggle if you are currently working as a full-time teacher'
              />
            </div>

            {/* Teaches Special Students Switch */}
            <div className='md:col-span-2'>
              <PrimarySwitchInput
                form={form}
                name='teachesSpecialStudents'
                label='Do you teach students with special needs?'
                description='Toggle if you have experience teaching students with special abilities'
              />
            </div>

            {/* Spoken Languages */}
            <div className='md:col-span-2'>
              <PrimaryMultiSelectForm
                form={form}
                name='spokenLanguages'
                label='Languages You Can Speak'
                options={languageOptions}
                placeholder='Select languages'
                required
              />
            </div>

            {/* Delivery Modes */}
            <div className='md:col-span-2'>
              <PrimaryMultiSelectForm
                form={form}
                name='deliveryModes'
                label='Where Do You Want to Teach?'
                options={deliveryModeOptions}
                placeholder='Select teaching modes'
                required
              />
            </div>

            {/* Location */}
            <div className='md:col-span-2'>
              <PrimaryInput
                form={form}
                name='location'
                label='Your Tuition Location'
                placeholder='Enter your primary teaching location'
                required
              />
            </div>

            {/* Description */}
            <div className='md:col-span-2'>
              <PrimaryTextarea
                form={form}
                name='description'
                label='Brief Yourself'
                placeholder='Describe your teaching experience, approach, and what makes you unique...'
                rows={4}
                required
              />
            </div>
          </div>

          <div className='flex gap-4 justify-end pt-4'>
            <CancelButton onClick={onCancel} disabled={isLoading} />
            <SubmitButton 
              isLoading={isLoading}
              loadingText={isEditing ? 'Updating...' : 'Creating...'}
            >
              {isEditing ? 'Update Information' : 'Create Information'}
            </SubmitButton>
          </div>
        </form>
      </Form>
    </div>
  );
};

export default TuitionInfoForm;
