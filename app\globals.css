@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 222.2 47.4% 11.2%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;
    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;
    --radius: 0.5rem;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
  }
}

@layer utilities {
  :root {
    --primary-color-50: 0 60% 97%;
    --primary-color-100: 3 67% 95%;
    --primary-color-200: 0 67% 90%;
    --primary-color-300: 0 62% 82%;
    --primary-color-400: 358 70% 72%;
    --primary-color-500: 357 65% 60%;
    --primary-color-600: 352 57% 50%;
    --primary-color-700: 351 61% 41%;
    --primary-color-800: 351 58% 35%;
    --primary-color-900: 351 54% 30%;
    --primary-color-950: 351 58% 16%;

    --secondary-color-50: 210, 100%, 97%;
    --secondary-color-100: 214, 95%, 93%;
    --secondary-color-200: 208, 97%, 85%;
    --secondary-color-300: 205, 94%, 74%;
    --secondary-color-400: 202, 89%, 59%;
    --secondary-color-500: 204, 89%, 47%;
    --secondary-color-600: 205, 97%, 38%;
    --secondary-color-700: 207, 97%, 32%;
    --secondary-color-800: 208, 91%, 27%;
    --secondary-color-900: 207, 83%, 24%;
    --secondary-color-950: 209, 85%, 15%;
  }

  ::selection {
    @apply bg-secondaryColor text-white;
  }

  ::-webkit-scrollbar {
    @apply w-2;
  }

  ::-webkit-scrollbar-track {
    @apply bg-gray-100;
  }

  ::-webkit-scrollbar-thumb {
    @apply bg-gray-600 hover:bg-secondaryColor-900;
  }

  .scrollbar-thin {
    scrollbar-width: thin;
  }

  .scrollbar-thumb-gray-200 {
    scrollbar-color: #e5e7eb transparent;
  }

  .scrollbar-thumb-gray-300 {
    scrollbar-color: #d1d5db transparent;
  }

  .scrollbar-track-transparent {
    scrollbar-track-color: transparent;
  }

  .max-container {
    @apply mx-auto max-w-[1400px];
  }

  .padding-container {
    @apply p-6 lg:p-10 xl:p-12;
  }

  .padding-container-lg {
    @apply p-6 lg:px-12 lg:py-20;
  }

  .padding-container-y {
    @apply py-6 lg:py-20;
  }

  .padding-container-x {
    @apply px-6 lg:px-12;
  }

  .padding-container-sm-y {
    @apply py-6 lg:py-8;
  }

  .btn-default {
    @apply bg-primaryColor text-white hover:bg-secondaryColor inline-flex shrink-0 items-center justify-center py-3 px-6 rounded-lg text-lg font-semibold hover:shadow-2xl;
  }

  .btn-default-sm {
    @apply bg-primaryColor text-white hover:bg-secondaryColor inline-flex shrink-0 items-center justify-center py-2 px-5 rounded-lg text-sm font-semibold hover:shadow-2xl;
  }

  .btn-default-md {
    @apply bg-primaryColor text-white hover:bg-secondaryColor inline-flex shrink-0 items-center justify-center py-3 px-6 rounded-lg text-sm font-semibold hover:shadow-2xl;
  }

  .btn-default__outline {
    @apply border-2 border-primaryColor text-primaryColor inline-flex shrink-0 items-center justify-center hover:bg-primaryColor hover:text-white py-3 px-6 rounded-lg text-lg font-semibold hover:shadow-2xl;
  }

  .btn-default__outline-sm {
    @apply border-2 border-primaryColor text-primaryColor inline-flex shrink-0 items-center justify-center hover:bg-primaryColor hover:text-white py-2 px-5 rounded-lg text-sm font-semibold hover:shadow-2xl;
  }

  .btn-default__outline-md {
    @apply border-2 border-primaryColor text-primaryColor inline-flex shrink-0 items-center justify-center hover:bg-primaryColor hover:text-white py-3 px-6 rounded-lg text-sm font-semibold hover:shadow-2xl;
  }

  button:disabled {
    @apply opacity-50 cursor-not-allowed;
  }

  .section-subtitle {
    @apply uppercase font-semibold text-primaryColor text-lg;
  }

  .section-subtitle-2 {
    @apply uppercase font-semibold text-primaryColor text-sm bg-primaryColor-50 py-2 px-4 rounded-sm tracking-[2px] inline-block;
  }

  .section-title {
    @apply text-xl leading-snug md:text-2xl xl:text-4xl lg:leading-normal font-bold;
  }

  .section-desc {
    @apply text-gray-600 text-[17px];
  }

  .bg-gradient-1 {
    @apply bg-my-gradient-1;
  }

  .text-gradient-1 {
    @apply bg-clip-text text-transparent bg-gradient-1;
  }

  .primary-link {
    @apply text-primaryColor font-semibold;
  }

  /* FORMS */

  .primary-tab-container {
    @apply flex flex-col gap-6 lg:flex-row lg:gap-12;
  }

  .primary-tab-trigger {
    @apply w-full justify-start py-2.5 data-[state="active"]:pl-6 data-[state="active"]:text-primaryColor data-[state="active"]:!shadow-none data-[state="active"]:rounded-3xl;
  }

  .primary-tab-list {
    @apply max-w-full w-full max-lg:flex-wrap flex lg:flex-col items-start justify-start h-full lg:w-64 py-6 px-4 rounded-3xl shrink-0;
  }

  .primary-input {
    @apply !py-6 focus-visible:!ring-primaryColor !ring-offset-primaryColor-50;
  }

  .primary-textarea {
    @apply focus-visible:!ring-primaryColor !ring-offset-primaryColor-50;
  }

  .primary-label {
    @apply flex items-center justify-start gap-2 text-sm text-slate-600;
  }

  .primary-select {
    @apply !py-6 text-slate-500 focus-visible:!ring-primaryColor !ring-offset-primaryColor-50;
  }

  .primary-checkbox,
  .primary-radio {
    @apply !accent-primaryColor;
  }

  input[type='number']::-webkit-outer-spin-button,
  input[type='number']::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
  }

  input[type='number'] {
    -moz-appearance: textfield;
  }

  .attachment-input {
    @apply inline-block w-full h-12 text-sm text-gray-900 border border-gray-300 bg-gray-50 rounded-lg cursor-pointer focus:outline-none;
  }

  .attachment-input::-webkit-file-upload-button {
    @apply inline-block px-4 py-2 text-sm mr-3 text-white bg-primaryColor outline-none border-none cursor-pointer focus:outline-none w-32 h-full;
  }

  .attachment-input::-webkit-file-upload-button:active,
  .attachment-input::-webkit-file-upload-button:hover {
    @apply opacity-70;
  }

  /* ANIMATIONS, OTHERS */
  .swiper-pagination-bullet-active {
    @apply !bg-primaryColor-500;
  }

  .swiper-pagination {
    bottom: -20px !important;
  }

  /* CUSTOM SCROLLBAR */

  .no-scrollbar {
    scrollbar-width: none;
    -ms-overflow-style: none;
  }

  .custom-scrollbar {
    scrollbar-width: thin;
    scrollbar-color: #4a5568 #edf2f7;
  }

  .custom-scrollbar::-webkit-scrollbar {
    width: 8px;
  }

  .custom-scrollbar::-webkit-scrollbar-track {
    background: #edf2f7;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb {
    background-color: #4a5568;
    border-radius: 20px;
    border: 3px solid #edf2f7;
  }

  /* Custom styles for react-phone-input-2 */
  .phone-input-container .react-tel-input .form-control {
    width: 100%;
    border: 1px solid #d1d5db;
    border-radius: 0.375rem;
    padding: 0.5rem 1rem;
    font-size: 1rem;
    height: 48px !important;
    padding-left: 52px !important;
  }

  .phone-input-container .react-tel-input .flag-dropdown {
    border-right: none;
    background-color: transparent;
  }

  .phone-input-container .react-tel-input .selected-flag {
    background-color: transparent;
    padding-left: 12px;
  }

  .phone-input-container .react-tel-input .selected-flag:hover,
  .phone-input-container .react-tel-input .selected-flag:focus {
    background-color: transparent;
  }

  .phone-input-container .react-tel-input .country-list {
    font-size: 1rem;
  }

  /* HIDE SCROLLBAR */

  .hide-scrollbar::-webkit-scrollbar {
    display: none;
  }
  .hide-scrollbar {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }
}
