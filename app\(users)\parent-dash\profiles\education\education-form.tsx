'use client';

import { School, Award, GraduationCap } from 'lucide-react';
import {
  PrimaryInput,
  PrimarySelect,
  PrimaryDateInput,
  ImageAndPdfUploader,
  SubmitButton,
  CancelButton,
  PrimaryAutoCompleteInput,
} from '@/components/forms';
import { PrimaryModalWithHeader } from '@/components/dashboard/shared/misc';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { toast } from 'react-toastify';
import { Form } from '@/components/ui/form';
import { cn } from '@/lib/utils';
import { scoreTypeOptions, educationTypeMap, IEducationTypeMap } from '@/validation/schemas/parent/education.maps';
import { IEducationDetailDocument } from '@/server/services/profile.service';
import { useCreateEducationDetail, useUpdateEducationDetail } from '@/hooks/profile/profile.hooks';
import { useGetAllBoards, useGetAllClasses } from '@/hooks/education/school.hooks';
import { createSelectOptions } from '@/validation/utils/form.utils';
import { useGetAllDegreeLevels, useGetAllDegrees, useGetAllBranches, useGetAllStreams } from '@/hooks/education/college.hooks';
import { CreateEducationDetailInput, createEducationDetailSchema } from '@/validation/schemas/parent/education-detail.schema';
import React, { useEffect } from 'react';
import { useUserSearchBusinessLocation } from '@/hooks/users/user.hooks';

interface EducationFormProps {
  isOpen: boolean;
  onClose: () => void;
  education?: IEducationDetailDocument;
  childProfileId: string;
}

const typeColorMap = {
  school: {
    light: 'bg-blue-50',
    medium: 'bg-blue-100/50',
    dark: 'bg-blue-100',
    darker: 'bg-blue-200',
    text: 'text-blue-600',
    textGray: 'text-gray-500',
    gradient: 'bg-gradient-to-br from-blue-500 to-blue-600',
    gradientHover: 'bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700',
    gradientBlur: 'bg-gradient-to-br from-blue-400 to-blue-600',
    border: 'border-blue-200',
    shadow: 'shadow-blue-100',
    activeGradient: 'bg-gradient-to-r from-blue-50 to-blue-100/70',
    iconColor: 'rgb(37 99 235)',
    grayColor: 'rgb(107 114 128)',
  },
  degree: {
    light: 'bg-green-50',
    medium: 'bg-green-100/50',
    dark: 'bg-green-100',
    darker: 'bg-green-200',
    text: 'text-green-600',
    textGray: 'text-gray-500',
    gradient: 'bg-gradient-to-br from-green-500 to-green-600',
    gradientHover: 'bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700',
    gradientBlur: 'bg-gradient-to-br from-green-400 to-green-600',
    border: 'border-green-200',
    shadow: 'shadow-green-100',
    activeGradient: 'bg-gradient-to-r from-green-50 to-green-100/70',
    iconColor: 'rgb(22 163 74)',
    grayColor: 'rgb(107 114 128)',
  },
  other: {
    light: 'bg-purple-50',
    medium: 'bg-purple-100/50',
    dark: 'bg-purple-100',
    darker: 'bg-purple-200',
    text: 'text-purple-600',
    textGray: 'text-gray-500',
    gradient: 'bg-gradient-to-br from-purple-500 to-purple-600',
    gradientHover: 'bg-gradient-to-r from-purple-500 to-purple-600 hover:from-purple-600 hover:to-purple-700',
    gradientBlur: 'bg-gradient-to-br from-purple-400 to-purple-600',
    border: 'border-purple-200',
    shadow: 'shadow-purple-100',
    activeGradient: 'bg-gradient-to-r from-purple-50 to-purple-100/70',
    iconColor: 'rgb(147 51 234)',
    grayColor: 'rgb(107 114 128)',
  },
};

const EducationForm = ({ isOpen, onClose, education, childProfileId }: EducationFormProps) => {
  if (!isOpen) return null;

  const createEducationDetail = useCreateEducationDetail();
  const updateEducationDetail = useUpdateEducationDetail();

  const isSubmitting = createEducationDetail.isPending || updateEducationDetail.isPending;

  const form = useForm<CreateEducationDetailInput>({
    resolver: zodResolver(createEducationDetailSchema),
    mode: 'onChange',
    defaultValues: {
      educationType: education?.educationType || educationTypeMap.school.key,
      childProfileId: education?.childProfileId || childProfileId,
      // School fields
      classId: education?.classId || '',
      boardId: education?.boardId || '',
      schoolName: education?.businessLocationDetails?.name || education?.schoolName || '',
      // College fields
      degreeLevelId: education?.degreeLevelId || '',
      degreeId: education?.degreeId || '',
      branchId: education?.branchId || '',
      collegeName: education?.businessLocationDetails?.name || education?.collegeName || '',
      // Achievement fields
      certificateName: education?.certificateName || '',
      certificateBy: education?.businessLocationDetails?.name || education?.certificateBy || '',
      certificateFor: education?.certificateFor || '',
      // Common fields
      businessLocationId: education?.businessLocationId || '',
      location: education?.businessLocationDetails?.location || education?.location || '',
      startDate: education?.startDate ? new Date(education.startDate) : new Date(),
      endDate: education?.endDate ? new Date(education.endDate) : undefined,
      scoreType: education?.scoreType || 'percentage',
      obtainedValue: education?.obtainedValue || '',
      maximumValue: education?.maximumValue || 100,
      certificateNumber: education?.certificateNumber || '',
      attachmentUrl: education?.attachmentUrl || '',
    },
  });

  const [businessSearchTerm, setBusinessSearchTerm] = React.useState('');

  const { data: businessLocationsData } = useUserSearchBusinessLocation(businessSearchTerm);

  const businessAutocompleteOptions = React.useMemo(() => {
    return (businessLocationsData?.data?.businessLocations || []).map((business) => ({
      value: business._id,
      label: `${business.name}${business.location ? ` - ${business.location}` : ''}`,
    }));
  }, [businessLocationsData]);

  const handleBusinessSelection = (nameField: 'schoolName' | 'collegeName' | 'certificateBy', selectedValue: string) => {
    const selectedBusiness = businessLocationsData?.data?.businessLocations?.find((business) => business._id === selectedValue);

    if (selectedBusiness) {
      form.setValue('businessLocationId', selectedBusiness._id);
      if (selectedBusiness.location) {
        form.setValue('location', selectedBusiness.location);
      }
      form.setValue(nameField, selectedBusiness.name);
    }
  };

  const handleTypeChange = (newType: IEducationTypeMap) => {
    form.setValue('educationType', newType);
  };

  const scoreType = form.watch('scoreType');

  useEffect(() => {
    if (scoreType === 'percentage') {
      form.setValue('maximumValue', 100);
    } else if (scoreType === 'cgpa') {
      form.setValue('maximumValue', 10);
    } else if (scoreType === 'other') {
      form.setValue('maximumValue', 100);
    }

    if (!education) {
      form.setValue('obtainedValue', '');
    }
  }, [scoreType, form, education]);

  const onSubmit = async (values: CreateEducationDetailInput) => {
    try {
      const displayLabel = educationTypeMap[values.educationType as IEducationTypeMap]?.label || educationTypeMap.school.label;

      const apiValues = {
        childProfileId: childProfileId,
        educationType: values.educationType,
        businessLocationId: values.businessLocationId || '',
        location: values.location,
        startDate: values.startDate instanceof Date ? values.startDate.toISOString() : values.startDate,
        endDate: values.endDate instanceof Date ? values.endDate.toISOString() : values.endDate,
        scoreType: values.scoreType,
        obtainedValue: values.obtainedValue,
        maximumValue: Number(values.maximumValue || '100'),
        certificateNumber: values.certificateNumber,
        ...(values.educationType === educationTypeMap.school.key
          ? {
              schoolName: values.schoolName,
              boardId: values.boardId,
              classId: values.classId,
            }
          : values.educationType === educationTypeMap.degree.key
          ? {
              degreeLevelId: values.degreeLevelId,
              degreeId: values.degreeId,
              branchId: values.branchId,
              collegeName: values.collegeName,
            }
          : {
              certificateName: values.certificateName,
              certificateFor: values.certificateFor || '',
              certificateBy: values.certificateBy,
            }),
      };

      const formData = new FormData();

      Object.entries(apiValues).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          formData.append(key, String(value));
        }
      });

      if (values.attachmentUrl && typeof values.attachmentUrl === 'object') {
        formData.append('attachmentUrl', values.attachmentUrl);
      }

      if (education) {
        const result = await updateEducationDetail.mutateAsync({ id: education._id, data: formData, childProfileId: childProfileId });
        if (!result.success) throw new Error(result.message || 'Operation failed');
        toast.success(`${displayLabel} updated successfully!`);
      } else {
        const result = await createEducationDetail.mutateAsync(formData);
        if (!result.success) throw new Error(result.message || 'Operation failed');
        toast.success(`${displayLabel} added successfully!`);
      }

      form.reset();
      onClose();
    } catch (error: any) {
      toast.error(error.message || 'Failed to add education details');
      console.error(error);
    }
  };

  const currentEducationType = form.watch('educationType') as IEducationTypeMap;
  const displayType = educationTypeMap[currentEducationType]?.label || educationTypeMap.school.label;

  // School specific data
  const boardId = form.watch('boardId');
  const { data: boardsData, isLoading: isLoadingBoards } = useGetAllBoards({ limit: 100 });
  const { data: classesData, isLoading: isLoadingClasses } = useGetAllClasses({ board: boardId, limit: 100 }, { enabled: !!boardId });

  const boardOptions = createSelectOptions({ items: boardsData?.data?.boards, keyMapping: '_id', labelKey: 'name' });
  const classOptions = createSelectOptions({ items: classesData?.data?.classes, keyMapping: '_id', labelKey: 'name' });

  // College specific data
  const streamId = form.watch('streamId');
  const degreeLevelId = form.watch('degreeLevelId');
  const degreeId = form.watch('degreeId');
  const { data: streamsData, isLoading: isLoadingStreams } = useGetAllStreams({ limit: 100 });
  const { data: degreeLevelsData, isLoading: isLoadingDegreeLevels } = useGetAllDegreeLevels(
    { limit: 100, stream: streamId },
    { enabled: !!streamId }
  );
  const { data: degreesData, isLoading: isLoadingDegrees } = useGetAllDegrees(
    { limit: 100, degreeLevel: degreeLevelId },
    { enabled: !!degreeLevelId }
  );
  const { data: branchesData, isLoading: isLoadingBranches } = useGetAllBranches({ degree: degreeId, limit: 100 }, { enabled: !!degreeId });

  const streamOptions = createSelectOptions({ items: streamsData?.data?.streams, keyMapping: '_id', labelKey: 'name' });
  const degreeLevelOptions = createSelectOptions({ items: degreeLevelsData?.data?.degreeLevels, keyMapping: '_id', labelKey: 'name' });
  const degreeOptions = createSelectOptions({ items: degreesData?.data?.degrees, keyMapping: '_id', labelKey: 'name' });
  const branchOptions = createSelectOptions({ items: branchesData?.data?.branches, keyMapping: '_id', labelKey: 'name' });

  const formVariant = currentEducationType === 'school' ? 'secondary' : currentEducationType === 'degree' ? 'green' : 'purple';

  const educationTypeMapToDisplay = education?.educationType
    ? Object.entries(educationTypeMap).filter(([key]) => key === education.educationType)
    : Object.entries(educationTypeMap);

  return (
    <PrimaryModalWithHeader
      isOpen={isOpen}
      onClose={onClose}
      title={`${education ? 'Edit' : 'Add'} ${displayType}`}
      subtitle={`${education ? 'Edit' : 'Add'} ${displayType.toLowerCase()} details`}
      icon={
        currentEducationType === 'school' ? (
          <School className='text-white' size={22} />
        ) : currentEducationType === 'degree' ? (
          <GraduationCap className='text-white' size={22} />
        ) : (
          <Award className='text-white' size={22} />
        )
      }
      variant={formVariant}
      maxWidth='max-w-7xl'
    >
      <Form {...form} key={education ? `edit-${education._id}` : 'add-new'}>
        <form onSubmit={form.handleSubmit(onSubmit)} className='p-8'>
          <div className='flex flex-col md:flex-row gap-8'>
            {/* Left Column - Type Selection */}
            <div className='md:w-1/4'>
              <div className='sticky top-0'>
                <h3 className='text-lg font-semibold text-gray-700 mb-4'>Record Type</h3>
                <div className='flex flex-col gap-3'>
                  {/* Record Type Buttons */}
                  {educationTypeMapToDisplay.map(([key, value]) => {
                    const typeKey = key as IEducationTypeMap;
                    const isActive = currentEducationType === typeKey;

                    const colorScheme = {
                      school: {
                        bg: isActive
                          ? `${typeColorMap.school.activeGradient} ${typeColorMap.school.border} text-blue-700 ${typeColorMap.school.shadow}`
                          : '',
                        icon: isActive ? typeColorMap.school.dark : 'bg-gray-100',
                        text: isActive ? typeColorMap.school.text : typeColorMap.school.textGray,
                        decoration: typeColorMap.school.dark,
                        decorationDark: typeColorMap.school.darker,
                        decorationLight: typeColorMap.school.medium,
                        iconColor: isActive ? typeColorMap.school.iconColor : typeColorMap.school.grayColor,
                      },
                      degree: {
                        bg: isActive
                          ? `${typeColorMap.degree.activeGradient} ${typeColorMap.degree.border} text-green-700 ${typeColorMap.degree.shadow}`
                          : '',
                        icon: isActive ? typeColorMap.degree.dark : 'bg-gray-100',
                        text: isActive ? typeColorMap.degree.text : typeColorMap.degree.textGray,
                        decoration: typeColorMap.degree.dark,
                        decorationDark: typeColorMap.degree.darker,
                        decorationLight: typeColorMap.degree.medium,
                        iconColor: isActive ? typeColorMap.degree.iconColor : typeColorMap.degree.grayColor,
                      },
                      other: {
                        bg: isActive
                          ? `${typeColorMap.other.activeGradient} ${typeColorMap.other.border} text-purple-700 ${typeColorMap.other.shadow}`
                          : '',
                        icon: isActive ? typeColorMap.other.dark : 'bg-gray-100',
                        text: isActive ? typeColorMap.other.text : typeColorMap.other.textGray,
                        decoration: typeColorMap.other.dark,
                        decorationDark: typeColorMap.other.darker,
                        decorationLight: typeColorMap.other.medium,
                        iconColor: isActive ? typeColorMap.other.iconColor : typeColorMap.other.grayColor,
                      },
                    };

                    const colors = colorScheme[typeKey];

                    return (
                      <button
                        key={typeKey}
                        type='button'
                        onClick={() => handleTypeChange(typeKey)}
                        className={cn(
                          'relative overflow-hidden flex items-center gap-3 px-4 py-2 rounded-xl border-2 shadow-sm transition-all duration-300',
                          colors.bg || 'bg-white border-gray-200 text-gray-700 hover:bg-gray-50'
                        )}
                      >
                        {isActive && (
                          <div className='absolute inset-0 pointer-events-none overflow-hidden'>
                            <div className={`absolute -right-4 top-4 w-12 h-12 rounded-full ${colors.decoration} transition-opacity`}></div>
                            <div className={`absolute left-2 top-2 w-2 h-2 rounded-full ${colors.decorationDark} transition-opacity`}></div>
                            <div className={`absolute -left-2 top-2 w-6 h-6 rotate-45 ${colors.decorationLight} transition-opacity`}></div>
                          </div>
                        )}
                        <div className={cn('w-10 h-10 rounded-lg flex items-center justify-center transition-colors duration-300', colors.icon)}>
                          {typeKey === 'school' && <School size={16} className={colors.text} />}
                          {typeKey === 'degree' && <GraduationCap size={16} className={colors.text} />}
                          {typeKey === 'other' && <Award size={16} className={colors.text} />}
                        </div>
                        <span className='font-medium'>{value.label}</span>
                      </button>
                    );
                  })}
                </div>

                {/* Upload Certificate Section */}
                <div className='mt-8'>
                  <ImageAndPdfUploader form={form} name='attachmentUrl' label='Certificate' variant={formVariant} required />
                </div>
              </div>
            </div>

            {/* Right Column - Form Fields */}
            <div className='md:w-3/4'>
              <h3 className='text-lg font-semibold text-gray-700 mb-6'>{`${displayType} Details`}</h3>
              <div className='grid grid-cols-1 md:grid-cols-3 gap-6'>
                {/* School-specific fields */}
                {currentEducationType === educationTypeMap.school.key && (
                  <>
                    <PrimarySelect
                      form={form}
                      name='boardId'
                      label='Board'
                      placeholder='Select a board'
                      options={boardOptions}
                      isLoading={isLoadingBoards}
                      required
                      variant={formVariant}
                    />
                    <PrimarySelect
                      form={form}
                      name='classId'
                      label='Class'
                      placeholder='Select a class'
                      options={classOptions}
                      isLoading={isLoadingClasses}
                      disabled={!boardId || isLoadingClasses}
                      required
                      variant={formVariant}
                    />
                    <div className='md:col-span-3'>
                      <PrimaryAutoCompleteInput
                        form={form}
                        name='schoolName'
                        label='School Name'
                        placeholder='Search for school or enter manually...'
                        variant={formVariant}
                        options={businessAutocompleteOptions}
                        icon={<School className='h-5 w-5 text-gray-400' />}
                        onSelect={(selectedValue) => handleBusinessSelection('schoolName', selectedValue)}
                        onInputChange={(value) => setBusinessSearchTerm(value)}
                        required
                      />
                    </div>
                  </>
                )}

                {/* Degree-specific fields */}
                {currentEducationType === educationTypeMap.degree.key && (
                  <>
                    <PrimarySelect
                      form={form}
                      name='streamId'
                      label='Stream'
                      placeholder='Select stream'
                      options={streamOptions}
                      isLoading={isLoadingStreams}
                      required
                      variant={formVariant}
                    />
                    <PrimarySelect
                      form={form}
                      name='degreeLevelId'
                      label='Degree Level'
                      placeholder='Select degree level'
                      options={degreeLevelOptions}
                      isLoading={isLoadingDegreeLevels}
                      disabled={!streamId || isLoadingDegreeLevels}
                      required
                      variant={formVariant}
                    />
                    <PrimarySelect
                      form={form}
                      name='degreeId'
                      label='Degree'
                      placeholder='Select degree'
                      options={degreeOptions}
                      isLoading={isLoadingDegrees}
                      disabled={!degreeLevelId || isLoadingDegrees}
                      required
                      variant={formVariant}
                    />
                    <PrimarySelect
                      form={form}
                      name='branchId'
                      label='Branch'
                      placeholder='Select branch'
                      options={branchOptions}
                      isLoading={isLoadingBranches}
                      disabled={!degreeId || isLoadingBranches}
                      variant={formVariant}
                    />
                    <div className='md:col-span-2'>
                      <PrimaryAutoCompleteInput
                        form={form}
                        name='collegeName'
                        label='College/Institute Name'
                        placeholder='Search for college/institute or enter manually...'
                        variant={formVariant}
                        options={businessAutocompleteOptions}
                        icon={<GraduationCap className='h-5 w-5 text-gray-400' />}
                        onSelect={(selectedValue) => handleBusinessSelection('collegeName', selectedValue)}
                        onInputChange={(value) => setBusinessSearchTerm(value)}
                        required
                      />
                    </div>
                  </>
                )}

                {/* Achievement-specific fields */}
                {currentEducationType === educationTypeMap.other.key && (
                  <>
                    <div className='md:col-span-3'>
                      <PrimaryInput
                        form={form}
                        name='certificateName'
                        label='Certificate/Achievement Name'
                        placeholder='e.g. Science Olympiad'
                        required
                        variant={formVariant}
                      />
                    </div>
                    <div className='md:col-span-3 grid md:grid-cols-2 gap-4'>
                      <PrimaryAutoCompleteInput
                        form={form}
                        name='certificateBy'
                        label='Issuing Organization'
                        placeholder='Search for organization or enter manually...'
                        variant={formVariant}
                        options={businessAutocompleteOptions}
                        icon={<Award className='h-5 w-5 text-gray-400' />}
                        onSelect={(selectedValue) => handleBusinessSelection('certificateBy', selectedValue)}
                        onInputChange={(value) => setBusinessSearchTerm(value)}
                        required
                      />

                      <PrimaryInput
                        form={form}
                        name='certificateFor'
                        label='Certificate For'
                        placeholder='e.g. Science Olympiad'
                        required
                        variant={formVariant}
                      />
                    </div>
                  </>
                )}

                {/* Common fields */}
                <PrimaryInput form={form} name='location' label='Location' placeholder='e.g. New Delhi' required variant={formVariant} />
                <PrimaryDateInput form={form} name='startDate' label='Start Date' required />
                <PrimaryDateInput form={form} name='endDate' label='End Date' />

                <PrimarySelect
                  key={`score-type-${education?._id || 'new'}`}
                  form={form}
                  name='scoreType'
                  label='Score Type'
                  options={scoreTypeOptions}
                  required
                  variant={formVariant}
                />

                <div className='flex gap-3'>
                  <PrimaryInput
                    form={form}
                    name='obtainedValue'
                    label={
                      form.watch('scoreType') === 'percentage'
                        ? 'Percentage Obtained'
                        : form.watch('scoreType') === 'cgpa'
                        ? 'CGPA Obtained'
                        : form.watch('scoreType') === 'grade'
                        ? 'Grade Obtained'
                        : form.watch('scoreType') === 'other'
                        ? 'Rank Obtained'
                        : 'Score Obtained'
                    }
                    placeholder={
                      form.watch('scoreType') === 'percentage'
                        ? 'e.g. 92 (0-100)'
                        : form.watch('scoreType') === 'cgpa'
                        ? 'e.g. 9.2 (0-10)'
                        : form.watch('scoreType') === 'grade'
                        ? 'e.g. A, B+, etc.'
                        : form.watch('scoreType') === 'other'
                        ? 'e.g. 5 (Rank)'
                        : 'Enter score'
                    }
                    required
                    variant={formVariant}
                  />
                  {form.watch('scoreType') !== 'grade' && (
                    <PrimaryInput
                      form={form}
                      name='maximumValue'
                      label='Max Value'
                      type='number'
                      placeholder={
                        form.watch('scoreType') === 'percentage'
                          ? '100'
                          : form.watch('scoreType') === 'cgpa'
                          ? '10'
                          : form.watch('scoreType') === 'other'
                          ? 'e.g. 100 (Total participants)'
                          : 'Max'
                      }
                      required
                      variant={formVariant}
                    />
                  )}
                </div>

                <PrimaryInput
                  form={form}
                  name='certificateNumber'
                  label='Certificate Number'
                  placeholder='e.g. CBSE-2023-45678'
                  variant={formVariant}
                />
              </div>

              {/* Footer with actions */}
              <div className='mt-8 flex justify-end gap-3 border-t border-gray-100 pt-5'>
                <CancelButton onClose={onClose} />
                <SubmitButton
                  isSubmitting={isSubmitting}
                  label={`${education ? 'Update' : 'Save'} ${displayType}`}
                  submittingLabel='Saving...'
                  variant={formVariant}
                />
              </div>
            </div>
          </div>
        </form>
      </Form>
    </PrimaryModalWithHeader>
  );
};

export default EducationForm;
